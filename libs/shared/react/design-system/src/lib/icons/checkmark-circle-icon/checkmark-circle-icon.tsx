import IconWrapper from '../../../utils/icon-wrapper/icon-wrapper';
import React from 'react';

export type CheckmarkCircleIconProps = React.HTMLProps<SVGElement>;

export const CheckmarkCircleIcon = {
  LIGHT: (props: CheckmarkCircleIconProps) => (
    <IconWrapper title="Checkmark Circle" {...props}>
      <g>
        <path d="M16,31.85C7.26,31.85,.15,24.74,.15,16S7.26,.15,16,.15s15.85,7.11,15.85,15.85-7.11,15.85-15.85,15.85Zm0-29.7c-7.64,0-13.85,6.21-13.85,13.85s6.21,13.85,13.85,13.85,13.85-6.21,13.85-13.85S23.64,2.15,16,2.15Z" />
        <path d="M13.12,22.93c-.27,0-.52-.11-.71-.29l-5.16-5.16c-.39-.39-.39-1.02,0-1.41s1.02-.39,1.41,0l4.45,4.45,9.77-9.77c.39-.39,1.02-.39,1.41,0s.39,1.02,0,1.41l-10.47,10.47c-.19,.19-.44,.29-.71,.29Z" />
      </g>
    </IconWrapper>
  ),
  SMALL: (props: CheckmarkCircleIconProps) => (
    <IconWrapper title="Checkmark Circle" {...props} viewBox="0 0 104 104">
      <g>
        <path
          d="M52 102C79.6142 102 102 79.6142 102 52C102 24.3858 79.6142 2 52 2C24.3858 2 2 24.3858 2 52C2 79.6142 24.3858 102 52 102Z"
          stroke="#7BAB37"
          strokeWidth="3"
        />
        <path
          d="M65.784 43.3862L46.832 62.3382L38.217 53.7242"
          stroke="#7BAB37"
          strokeWidth="3"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
    </IconWrapper>
  ),
};

export default CheckmarkCircleIcon;
