import { Injectable, Logger } from '@nestjs/common';
import Strip<PERSON> from 'stripe';

export interface CreateCustomerRequest {
  name: string;
  email: string;
  address?: {
    line1?: string;
    line2?: string;
    city?: string;
    postalCode?: string;
    country?: string;
  };
}

export interface CreateCustomerResponse {
  customerId: string;
}

export interface RetrieveCustomerResponse {
  customerId: string;
  name?: string | null;
  defaultPaymentMethod?: string | null;
}

export interface UpdateCustomerRequest {
  name?: string;
  email: string;
  address?: {
    line1?: string;
    line2?: string;
    city?: string;
    postalCode?: string;
    country?: string;
  };
}

export interface RetrieveCashBalanceResponse {
  customerId: string;
  availableBalance: number;
  currency: string;
}

@Injectable()
export class StripeCustomerService {
  private readonly logger = new Logger(StripeCustomerService.name);

  constructor(private readonly stripe: Stripe) {}

  async create(
    request: CreateCustomerRequest
  ): Promise<CreateCustomerResponse> {
    this.logger.log({ request }, 'creating stripe customer');
    return this.stripe.customers
      .create({
        name: request.name,
        email: request.email,
        address: request.address
          ? {
              line1: request.address.line1,
              line2: request.address.line2,
              city: request.address.city,
              country: request.address.country,
              postal_code: request.address.postalCode,
            }
          : undefined,
      })
      .then((customer) => ({
        customerId: customer.id,
      }));
  }

  async retrieve(
    customerId: string
  ): Promise<RetrieveCustomerResponse | undefined> {
    this.logger.log({ customerId }, 'retrieving stripe customer');

    return this.stripe.customers.retrieve(customerId).then(async (customer) => {
      if (!customer || customer.deleted === true) {
        return undefined;
      }

      const defaultPaymentMethod =
        typeof customer.invoice_settings.default_payment_method === 'string'
          ? await this.stripe.customers.retrievePaymentMethod(
              customer.id,
              customer.invoice_settings.default_payment_method
            )
          : null;

      return {
        customerId: customer.id,
        name: customer.name,
        defaultPaymentMethod: defaultPaymentMethod?.type,
      };
    });
  }

  async update(
    customerId: string,
    update: UpdateCustomerRequest
  ): Promise<void> {
    this.logger.log({ customerId }, 'updating stripe customer');

    const updateParams: Stripe.CustomerUpdateParams = {
      email: update.email,
    };

    if (update.name) {
      updateParams.name = update.name;
    }

    if (update.address) {
      updateParams.address = {
        line1: update.address.line1,
        line2: update.address.line2,
        city: update.address.city,
        postal_code: update.address.postalCode,
        country: update.address.country,
      };
    }

    await this.stripe.customers.update(customerId, updateParams);
  }

  async retrieveCashBalance(
    customerId: string
  ): Promise<RetrieveCashBalanceResponse | undefined> {
    this.logger.log({ customerId }, 'retrieving stripe customer cash balance');

    try {
      const cashBalance = await this.stripe.customers.retrieveCashBalance(
        customerId
      );

      return {
        customerId: customerId,
        availableBalance: cashBalance.available?.gbp ?? 0,
        currency: 'GBP',
      };
    } catch (error) {
      this.logger.error(
        { customerId, error },
        'failed to retrieve customer cash balance'
      );
      return undefined;
    }
  }
}
