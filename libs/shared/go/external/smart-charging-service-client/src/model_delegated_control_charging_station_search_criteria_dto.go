/*
Smart Charging Service Api

API for managing smart charging service api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package smartchargingserviceclient

import (
	"encoding/json"
)

// checks if the DelegatedControlChargingStationSearchCriteriaDto type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &DelegatedControlChargingStationSearchCriteriaDto{}

// DelegatedControlChargingStationSearchCriteriaDto struct for DelegatedControlChargingStationSearchCriteriaDto
type DelegatedControlChargingStationSearchCriteriaDto struct {
	Status              *string `json:"status,omitempty"`
	HasVehiclePluggedIn *bool   `json:"hasVehiclePluggedIn,omitempty"`
	ProviderId          *string `json:"providerId,omitempty"`
}

// NewDelegatedControlChargingStationSearchCriteriaDto instantiates a new DelegatedControlChargingStationSearchCriteriaDto object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewDelegatedControlChargingStationSearchCriteriaDto() *DelegatedControlChargingStationSearchCriteriaDto {
	this := DelegatedControlChargingStationSearchCriteriaDto{}
	return &this
}

// NewDelegatedControlChargingStationSearchCriteriaDtoWithDefaults instantiates a new DelegatedControlChargingStationSearchCriteriaDto object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewDelegatedControlChargingStationSearchCriteriaDtoWithDefaults() *DelegatedControlChargingStationSearchCriteriaDto {
	this := DelegatedControlChargingStationSearchCriteriaDto{}
	return &this
}

// GetStatus returns the Status field value if set, zero value otherwise.
func (o *DelegatedControlChargingStationSearchCriteriaDto) GetStatus() string {
	if o == nil || IsNil(o.Status) {
		var ret string
		return ret
	}
	return *o.Status
}

// GetStatusOk returns a tuple with the Status field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DelegatedControlChargingStationSearchCriteriaDto) GetStatusOk() (*string, bool) {
	if o == nil || IsNil(o.Status) {
		return nil, false
	}
	return o.Status, true
}

// HasStatus returns a boolean if a field has been set.
func (o *DelegatedControlChargingStationSearchCriteriaDto) HasStatus() bool {
	if o != nil && !IsNil(o.Status) {
		return true
	}

	return false
}

// SetStatus gets a reference to the given string and assigns it to the Status field.
func (o *DelegatedControlChargingStationSearchCriteriaDto) SetStatus(v string) {
	o.Status = &v
}

// GetHasVehiclePluggedIn returns the HasVehiclePluggedIn field value if set, zero value otherwise.
func (o *DelegatedControlChargingStationSearchCriteriaDto) GetHasVehiclePluggedIn() bool {
	if o == nil || IsNil(o.HasVehiclePluggedIn) {
		var ret bool
		return ret
	}
	return *o.HasVehiclePluggedIn
}

// GetHasVehiclePluggedInOk returns a tuple with the HasVehiclePluggedIn field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DelegatedControlChargingStationSearchCriteriaDto) GetHasVehiclePluggedInOk() (*bool, bool) {
	if o == nil || IsNil(o.HasVehiclePluggedIn) {
		return nil, false
	}
	return o.HasVehiclePluggedIn, true
}

// HasHasVehiclePluggedIn returns a boolean if a field has been set.
func (o *DelegatedControlChargingStationSearchCriteriaDto) HasHasVehiclePluggedIn() bool {
	if o != nil && !IsNil(o.HasVehiclePluggedIn) {
		return true
	}

	return false
}

// SetHasVehiclePluggedIn gets a reference to the given bool and assigns it to the HasVehiclePluggedIn field.
func (o *DelegatedControlChargingStationSearchCriteriaDto) SetHasVehiclePluggedIn(v bool) {
	o.HasVehiclePluggedIn = &v
}

// GetProviderId returns the ProviderId field value if set, zero value otherwise.
func (o *DelegatedControlChargingStationSearchCriteriaDto) GetProviderId() string {
	if o == nil || IsNil(o.ProviderId) {
		var ret string
		return ret
	}
	return *o.ProviderId
}

// GetProviderIdOk returns a tuple with the ProviderId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DelegatedControlChargingStationSearchCriteriaDto) GetProviderIdOk() (*string, bool) {
	if o == nil || IsNil(o.ProviderId) {
		return nil, false
	}
	return o.ProviderId, true
}

// HasProviderId returns a boolean if a field has been set.
func (o *DelegatedControlChargingStationSearchCriteriaDto) HasProviderId() bool {
	if o != nil && !IsNil(o.ProviderId) {
		return true
	}

	return false
}

// SetProviderId gets a reference to the given string and assigns it to the ProviderId field.
func (o *DelegatedControlChargingStationSearchCriteriaDto) SetProviderId(v string) {
	o.ProviderId = &v
}

func (o DelegatedControlChargingStationSearchCriteriaDto) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o DelegatedControlChargingStationSearchCriteriaDto) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if !IsNil(o.Status) {
		toSerialize["status"] = o.Status
	}
	if !IsNil(o.HasVehiclePluggedIn) {
		toSerialize["hasVehiclePluggedIn"] = o.HasVehiclePluggedIn
	}
	if !IsNil(o.ProviderId) {
		toSerialize["providerId"] = o.ProviderId
	}
	return toSerialize, nil
}

type NullableDelegatedControlChargingStationSearchCriteriaDto struct {
	value *DelegatedControlChargingStationSearchCriteriaDto
	isSet bool
}

func (v NullableDelegatedControlChargingStationSearchCriteriaDto) Get() *DelegatedControlChargingStationSearchCriteriaDto {
	return v.value
}

func (v *NullableDelegatedControlChargingStationSearchCriteriaDto) Set(val *DelegatedControlChargingStationSearchCriteriaDto) {
	v.value = val
	v.isSet = true
}

func (v NullableDelegatedControlChargingStationSearchCriteriaDto) IsSet() bool {
	return v.isSet
}

func (v *NullableDelegatedControlChargingStationSearchCriteriaDto) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableDelegatedControlChargingStationSearchCriteriaDto(val *DelegatedControlChargingStationSearchCriteriaDto) *NullableDelegatedControlChargingStationSearchCriteriaDto {
	return &NullableDelegatedControlChargingStationSearchCriteriaDto{value: val, isSet: true}
}

func (v NullableDelegatedControlChargingStationSearchCriteriaDto) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableDelegatedControlChargingStationSearchCriteriaDto) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
