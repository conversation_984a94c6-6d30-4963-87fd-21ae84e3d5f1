import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Param,
  Post,
  Put,
  Res,
  StreamableFile,
  UseInterceptors,
} from '@nestjs/common';
import {
  CreateOrUpdateStripeSubscriptionRequest,
  CreateStripeConnectedAccountAccountLinkRequest,
  CreateStripeConnectedAccountRequest,
  CreateStripeCustomerRequest,
  SubscriptionInvoiceDto,
} from '@experience/commercial/statement-service/shared';
import { Response } from 'express';
import {
  RetrieveCashBalanceResponse,
  RetrieveSubscriptionResponse,
} from '@experience/shared/nest/stripe';
import { StripeInterceptor } from './stripe.interceptor';
import { StripeService } from './stripe.service';

@UseInterceptors(StripeInterceptor)
@Controller('stripe')
export class StripeController {
  constructor(private readonly stripeService: StripeService) {}

  @Post('/customers')
  async createStripeCustomerForGroup(
    @Body() request: CreateStripeCustomerRequest
  ): Promise<void> {
    await this.stripeService.createStripeCustomerForGroup(request);
  }

  @Get('/customers/:customerId/cash-balance')
  async getCustomerCashBalance(
    @Param('customerId') customerId: string
  ): Promise<RetrieveCashBalanceResponse | undefined> {
    return this.stripeService.getCustomerCashBalance(customerId);
  }

  @Post('/connected-accounts')
  async createStripeConnectedAccountForGroup(
    @Body() request: CreateStripeConnectedAccountRequest
  ): Promise<void> {
    await this.stripeService.createStripeConnectedAccountForGroup(request);
  }

  @Delete('/connected-accounts')
  @HttpCode(204)
  async removeStripeConnectedAccountForGroup(
    @Body() request: CreateStripeConnectedAccountRequest
  ): Promise<void> {
    await this.stripeService.removeStripeConnectedAccountFromGroup(request);
  }

  @Post('/connected-accounts/account-links')
  async createStripeConnectedAccountLink(
    @Body() request: CreateStripeConnectedAccountAccountLinkRequest
  ): Promise<string> {
    return this.stripeService.createStripeConnectedAccountLink(request);
  }

  @Post('/subscriptions')
  async createStripeSubscriptionForGroup(
    @Body() request: CreateOrUpdateStripeSubscriptionRequest
  ): Promise<void> {
    await this.stripeService.createStripeSubscriptionForGroup(request);
  }

  @Put('/subscriptions')
  async updateStripeSubscriptionForGroup(
    @Body() request: CreateOrUpdateStripeSubscriptionRequest
  ): Promise<void> {
    await this.stripeService.updateStripeSubscriptionForGroup(request);
  }

  @Get('/subscriptions/:subscriptionId')
  async getSubscription(
    @Param('subscriptionId') subscriptionId: string
  ): Promise<RetrieveSubscriptionResponse> {
    return this.stripeService.retrieveSubscription(subscriptionId);
  }

  @Get('/subscriptions/:subscriptionId/invoices')
  async getInvoicesBySubscriptionId(
    @Param('subscriptionId') subscriptionId: string
  ): Promise<SubscriptionInvoiceDto[]> {
    return this.stripeService.getInvoicesBySubscriptionId(subscriptionId);
  }

  @Get('/subscriptions/:subscriptionId/invoices/:invoiceId')
  async getSubscriptionInvoice(
    @Param('invoiceId') invoiceId: string,
    @Param('subscriptionId') subscriptionId: string
  ): Promise<SubscriptionInvoiceDto> {
    return this.stripeService.getSubscriptionInvoice(invoiceId, subscriptionId);
  }

  @Get('/subscriptions/:subscriptionId/invoices/:invoiceId/pdf')
  async getSubscriptionInvoicePdf(
    @Param('invoiceId') invoiceId: string,
    @Param('subscriptionId') subscriptionId: string,
    @Res({ passthrough: true }) res: Response
  ): Promise<StreamableFile> {
    const { filename, buffer } =
      await this.stripeService.getSubscriptionInvoicePdf(
        invoiceId,
        subscriptionId
      );

    res.set({
      'Content-Type': 'application/pdf',
      'Content-Disposition': `attachment; filename=${filename}`,
    });

    return new StreamableFile(buffer);
  }
}
