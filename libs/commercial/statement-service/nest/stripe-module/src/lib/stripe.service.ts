import { ConfigService } from '@nestjs/config';
import {
  CreateOrUpdateStripeSubscriptionRequest,
  CreateStripeConnectedAccountAccountLinkRequest,
  CreateStripeConnectedAccountRequest,
  CreateStripeCustomerRequest,
  RemoveStripeConnectedAccountRequest,
  SubscriptionInvoiceDto,
} from '@experience/commercial/statement-service/shared';
import {
  CreateStripeSubscriptionException,
  StripeConnectedAccountAlreadyExistsException,
  StripeConnectedAccountNotFoundException,
  StripeCustomerAlreadyExistsException,
  StripeCustomerDetailsMissingException,
  StripeCustomerNotFoundException,
  StripeCustomerSubscriptionIdMissingException,
  StripePriceNotFoundException,
  StripeSubscriptionAlreadyExistsException,
  StripeSubscriptionInvoiceNotFinalisedException,
  StripeSubscriptionInvoiceNotFoundException,
  StripeSubscriptionNotFoundException,
} from '@experience/commercial/statement-service/nest/shared';
import { Group } from '@experience/commercial/statement-service/shared';
import { GroupsService } from '@experience/commercial/statement-service/nest/groups-module';
import { HttpRedirectResponse, Injectable, Logger } from '@nestjs/common';
import {
  InvoiceResponse,
  RetrieveCashBalanceResponse,
  RetrieveSubscriptionResponse,
  StripeInvoiceService,
} from '@experience/shared/nest/stripe';
import {
  Prisma,
  StatementsPrismaClient,
} from '@experience/commercial/statement-service/prisma/statements/client';
import {
  StripeAccountService,
  StripeCustomerService,
  StripePricesService,
  StripeSubscriptionService,
} from '@experience/shared/nest/stripe';
import { assertIsDefined } from '@experience/shared/typescript/utils';
import axios from 'axios';
import dayjs from 'dayjs';

@Injectable()
export class StripeService {
  private readonly logger = new Logger(StripeService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly groupsService: GroupsService,
    private readonly statementsDatabase: StatementsPrismaClient,
    private readonly stripeAccountService: StripeAccountService,
    private readonly stripeInvoiceService: StripeInvoiceService,
    private readonly stripeCustomerService: StripeCustomerService,
    private readonly stripePrices: StripePricesService,
    private readonly stripeSubscription: StripeSubscriptionService
  ) {}

  async createStripeCustomerForGroup(
    request: CreateStripeCustomerRequest
  ): Promise<void> {
    this.logger.log({ request }, 'creating stripe customer for group');

    const group = await this.groupsService.findGroupByGroupId(request.groupId);

    if (group.stripeCustomerId) {
      throw new StripeCustomerAlreadyExistsException();
    }

    if (!group.businessName || !group.businessEmail) {
      throw new StripeCustomerDetailsMissingException();
    }

    const { customerId } = await this.stripeCustomerService.create({
      name: group.businessName,
      email: group.businessEmail,
      address: {
        line1: group.addressLine1,
        line2: group.addressLine2,
        city: group.town,
        postalCode: group.postcode,
        country: 'GB',
      },
    });

    await this.updateGroupConfigByGroupId(group.groupId, {
      stripeCustomerId: customerId,
    });
  }

  async createStripeConnectedAccountForGroup(
    request: CreateStripeConnectedAccountRequest
  ): Promise<string> {
    this.logger.log({ request }, 'creating stripe connected account for group');

    const group: Group = await this.groupsService.findGroupByGroupId(
      request.groupId
    );

    if (group.stripeConnectedAccountId) {
      throw new StripeConnectedAccountAlreadyExistsException();
    }

    if (!group.businessName || !group.businessEmail || !group.accountRef) {
      throw new StripeCustomerDetailsMissingException();
    }

    const { accountId } = await this.stripeAccountService.create({
      businessName: group.businessName,
      country: 'GB',
      email: group.businessEmail,
      type: 'standard',
    });

    await this.updateGroupConfigByGroupId(group.groupId, {
      stripeConnectedAccountId: accountId,
    });

    return accountId;
  }

  async removeStripeConnectedAccountFromGroup(
    request: RemoveStripeConnectedAccountRequest
  ): Promise<void> {
    this.logger.log({ request }, 'removing stripe connected account');

    const clientId = this.configService.get('STRIPE_CLIENT_ID') as string;

    const group = await this.groupsService.findGroupByGroupId(request.groupId);

    if (!group.stripeConnectedAccountId) {
      throw new StripeConnectedAccountNotFoundException();
    }

    await this.stripeAccountService.remove(
      clientId,
      group.stripeConnectedAccountId
    );

    await this.updateGroupConfigByGroupId(group.groupId, {
      stripeConnectedAccountId: null,
    });
  }

  async createStripeConnectedAccountLink(
    request: CreateStripeConnectedAccountAccountLinkRequest
  ): Promise<string> {
    this.logger.log(
      { request },
      'creating stripe connected account link for group'
    );
    const group = await this.groupsService.findGroupByGroupId(request.groupId);

    if (!group.stripeConnectedAccountId) {
      throw new StripeConnectedAccountNotFoundException();
    }

    return this.stripeAccountService.createAccountOnboardingLink(
      group.stripeConnectedAccountId,
      'https://sites.pod-point.com/billing'
    );
  }

  async refreshOnboardingLink(groupId: string): Promise<HttpRedirectResponse> {
    return {
      url: await this.createStripeConnectedAccountLink({ groupId }),
      statusCode: 307,
    };
  }

  async createStripeSubscriptionForGroup(
    request: CreateOrUpdateStripeSubscriptionRequest
  ): Promise<void> {
    this.logger.log({ request }, 'creating stripe subscription for group');

    const group = await this.groupsService.findGroupByGroupId(request.groupId);

    if (!group.stripeCustomerId) {
      throw new StripeCustomerNotFoundException();
    }

    if (group.stripeSubscriptionId) {
      throw new StripeSubscriptionAlreadyExistsException();
    }

    const price = await this.stripePrices.getPriceByLookupKey(
      'site_management_service_fee_monthly'
    );

    if (!price) {
      throw new StripePriceNotFoundException();
    }

    const taxRates =
      this.configService
        .get<string>('STRIPE_EXCLUSIVE_TAX_RATES')
        ?.split(',') ?? [];

    const subscription = await this.stripeSubscription.create({
      customerId: group.stripeCustomerId,
      prices: [
        {
          id: price.id,
          quantity: request.socketQuantity,
          taxRates,
        },
      ],
    });

    await this.updateGroupConfigByGroupId(group.groupId, {
      stripeSubscriptionId: subscription.id,
    });

    if (subscription.status === 'incomplete') {
      throw new CreateStripeSubscriptionException();
    }
  }

  async retrieveSubscription(
    subscriptionId: string
  ): Promise<RetrieveSubscriptionResponse> {
    this.logger.log({ subscriptionId }, 'retrieving stripe subscription');
    const subscription = await this.stripeSubscription.retrieve(subscriptionId);

    if (!subscription) {
      throw new StripeSubscriptionNotFoundException();
    }

    return subscription;
  }

  async updateStripeSubscriptionForGroup(
    request: CreateOrUpdateStripeSubscriptionRequest
  ): Promise<void> {
    this.logger.log({ request }, 'updating stripe subscription');

    const group = await this.groupsService.findGroupByGroupId(request.groupId);
    if (!group.stripeSubscriptionId) {
      throw new StripeCustomerSubscriptionIdMissingException();
    }

    const subscription = await this.stripeSubscription.retrieve(
      group.stripeSubscriptionId
    );
    if (!subscription) {
      throw new StripeSubscriptionNotFoundException();
    }

    const item = subscription.items.find(
      (item) => item.price.lookup_key === 'site_management_service_fee_monthly'
    );
    if (!item) {
      throw new StripePriceNotFoundException();
    }

    await this.stripeSubscription.update(group.stripeSubscriptionId, {
      items: [
        {
          id: item.id,
          quantity: request.socketQuantity,
        },
      ],
    });
  }

  async getSubscriptionInvoice(
    invoiceId: string,
    subscriptionId: string
  ): Promise<SubscriptionInvoiceDto> {
    this.logger.log({ invoiceId }, 'getting invoice');

    const invoice = await this.getInvoicesBySubscriptionId(subscriptionId).then(
      (invoices) => invoices.find((invoice) => invoice.id === invoiceId)
    );

    if (!invoice) {
      throw new StripeSubscriptionInvoiceNotFoundException();
    }

    return invoice;
  }

  async getSubscriptionInvoicePdf(
    invoiceId: string,
    subscriptionId: string
  ): Promise<{ filename: string; buffer: Buffer }> {
    this.logger.log({ invoiceId }, 'getting invoice pdf');

    const { invoiceNumber, invoicePdfUrl } = await this.getSubscriptionInvoice(
      invoiceId,
      subscriptionId
    );

    if (!invoicePdfUrl) {
      throw new StripeSubscriptionInvoiceNotFinalisedException();
    }

    const buffer = await axios
      .get(invoicePdfUrl, { responseType: 'arraybuffer' })
      .then((response) => Buffer.from(response.data));

    return { filename: `Invoice-${invoiceNumber}.pdf`, buffer };
  }

  async getInvoicesBySubscriptionId(
    subscriptionId: string
  ): Promise<SubscriptionInvoiceDto[]> {
    this.logger.log({ subscriptionId }, 'finding invoices by subscription id');

    return this.stripeInvoiceService
      .getInvoicesBySubscriptionId(subscriptionId)
      .then((invoices) =>
        invoices.map((invoice) => this.mapSubscriptionInvoice(invoice))
      );
  }

  private async updateGroupConfigByGroupId(
    groupId: string,
    data: Prisma.GroupConfigUpdateInput
  ) {
    await this.statementsDatabase.groupConfig.update({
      data,
      where: {
        groupId: groupId,
      },
    });
  }

  private mapSubscriptionInvoice(
    invoice: InvoiceResponse
  ): SubscriptionInvoiceDto {
    assertIsDefined(invoice.id, 'Invoice ID is not defined');
    return {
      amount: invoice.amount_due ?? 0,
      created: invoice.created
        ? dayjs.unix(invoice.created).format('YYYY-MM-DD')
        : '',
      due: invoice.due_date
        ? dayjs.unix(invoice.due_date).format('YYYY-MM-DD')
        : '',
      email: invoice.customer_email ?? '',
      hostedInvoiceUrl: invoice.hosted_invoice_url ?? '',
      id: invoice.id,
      invoiceNumber: invoice.number ?? '',
      invoicePdfUrl: invoice.invoice_pdf ?? undefined,
      status: invoice.status ?? '',
    };
  }

  async getCustomerCashBalance(
    customerId: string
  ): Promise<RetrieveCashBalanceResponse | undefined> {
    this.logger.log({ customerId }, 'retrieving customer cash balance');

    return this.stripeCustomerService.retrieveCashBalance(customerId);
  }
}
