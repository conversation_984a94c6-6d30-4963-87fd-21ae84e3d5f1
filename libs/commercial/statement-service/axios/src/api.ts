/* tslint:disable */
/* eslint-disable */
/**
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import type { Configuration } from './configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import {
  DUMMY_BASE_URL,
  assertParamExists,
  setApiKeyToObject,
  setBasicAuthToObject,
  setBearerAuthToObject,
  setOAuthToObject,
  setSearchParams,
  serializeDataIfNeeded,
  toPathString,
  createRequestFunction,
} from './common';
import type { RequestArgs } from './base';
// @ts-ignore
import {
  BASE_PATH,
  COLLECTION_FORMATS,
  BaseAPI,
  RequiredError,
  operationServerMap,
} from './base';

/**
 *
 * @export
 * @interface AdjustedFee
 */
export interface AdjustedFee {
  /**
   *
   * @type {number}
   * @memberof AdjustedFee
   */
  fee: number;
  /**
   *
   * @type {string}
   * @memberof AdjustedFee
   */
  ppid: string;
}
/**
 *
 * @export
 * @interface AssignUserToWorkItemRequest
 */
export interface AssignUserToWorkItemRequest {
  /**
   *
   * @type {string}
   * @memberof AssignUserToWorkItemRequest
   */
  email: string;
  /**
   *
   * @type {string}
   * @memberof AssignUserToWorkItemRequest
   */
  name: string;
}
/**
 *
 * @export
 * @interface Charge
 */
export interface Charge {
  /**
   *
   * @type {string}
   * @memberof Charge
   */
  chargingDuration: string;
  /**
   *
   * @type {boolean}
   * @memberof Charge
   */
  confirmed: boolean;
  /**
   *
   * @type {string}
   * @memberof Charge
   */
  confirmedBy?: ChargeConfirmedByEnum;
  /**
   *
   * @type {string}
   * @memberof Charge
   */
  co2Savings: string;
  /**
   *
   * @type {string}
   * @memberof Charge
   */
  door?: string;
  /**
   *
   * @type {string}
   * @memberof Charge
   */
  endedAt?: string;
  /**
   *
   * @type {string}
   * @memberof Charge
   */
  energyCost: string;
  /**
   *
   * @type {string}
   * @memberof Charge
   */
  energyUsage: string;
  /**
   *
   * @type {number}
   * @memberof Charge
   */
  locationId?: number;
  /**
   *
   * @type {string}
   * @memberof Charge
   */
  pluggedIn: string;
  /**
   *
   * @type {string}
   * @memberof Charge
   */
  podName?: string;
  /**
   *
   * @type {string}
   * @memberof Charge
   */
  ppid?: string;
  /**
   *
   * @type {string}
   * @memberof Charge
   */
  revenueGenerated: string;
  /**
   *
   * @type {string}
   * @memberof Charge
   */
  startedAt: string;
  /**
   *
   * @type {string}
   * @memberof Charge
   */
  siteName?: string;
  /**
   *
   * @type {string}
   * @memberof Charge
   */
  totalDuration?: string;
  /**
   *
   * @type {string}
   * @memberof Charge
   */
  userEmail?: string;
  /**
   *
   * @type {string}
   * @memberof Charge
   */
  userName?: string;
  /**
   *
   * @type {string}
   * @memberof Charge
   */
  vehicle?: string;
}

export const ChargeConfirmedByEnum = {
  Driver: 'driver',
  Other: 'other',
} as const;

export type ChargeConfirmedByEnum =
  (typeof ChargeConfirmedByEnum)[keyof typeof ChargeConfirmedByEnum];

/**
 *
 * @export
 * @interface ChargeSummary
 */
export interface ChargeSummary {
  /**
   *
   * @type {number}
   * @memberof ChargeSummary
   */
  chargingDuration: number;
  /**
   *
   * @type {number}
   * @memberof ChargeSummary
   */
  claimedEnergyUsage: number;
  /**
   *
   * @type {number}
   * @memberof ChargeSummary
   */
  co2Savings: number;
  /**
   *
   * @type {number}
   * @memberof ChargeSummary
   */
  energyCost: number;
  /**
   *
   * @type {number}
   * @memberof ChargeSummary
   */
  energyDelivered: number;
  /**
   *
   * @type {number}
   * @memberof ChargeSummary
   */
  energyUsage: number;
  /**
   *
   * @type {number}
   * @memberof ChargeSummary
   */
  numberOfCharges: number;
  /**
   *
   * @type {number}
   * @memberof ChargeSummary
   */
  revenueGenerated: number;
  /**
   *
   * @type {number}
   * @memberof ChargeSummary
   */
  revenueGeneratingClaimedUsage: number;
}
/**
 *
 * @export
 * @interface ChargerWithPod
 */
export interface ChargerWithPod {
  /**
   *
   * @type {Pod}
   * @memberof ChargerWithPod
   */
  pod: Pod;
  /**
   *
   * @type {number}
   * @memberof ChargerWithPod
   */
  fee: number | null;
  /**
   *
   * @type {string}
   * @memberof ChargerWithPod
   */
  ppid: string;
  /**
   *
   * @type {string}
   * @memberof ChargerWithPod
   */
  siteId: string;
}
/**
 *
 * @export
 * @interface Coordinates
 */
export interface Coordinates {
  /**
   *
   * @type {number}
   * @memberof Coordinates
   */
  latitude?: number;
  /**
   *
   * @type {number}
   * @memberof Coordinates
   */
  longitude?: number;
}
/**
 *
 * @export
 * @interface CreateGroupRequest
 */
export interface CreateGroupRequest {
  /**
   *
   * @type {string}
   * @memberof CreateGroupRequest
   */
  groupName: string;
  /**
   *
   * @type {string}
   * @memberof CreateGroupRequest
   */
  groupId: string;
  /**
   *
   * @type {string}
   * @memberof CreateGroupRequest
   */
  accountRef: string;
}
/**
 *
 * @export
 * @interface CreateOrUpdateStripeSubscriptionRequest
 */
export interface CreateOrUpdateStripeSubscriptionRequest {
  /**
   *
   * @type {string}
   * @memberof CreateOrUpdateStripeSubscriptionRequest
   */
  groupId: string;
  /**
   *
   * @type {number}
   * @memberof CreateOrUpdateStripeSubscriptionRequest
   */
  socketQuantity: number;
}
/**
 *
 * @export
 * @interface CreateStatementRequest
 */
export interface CreateStatementRequest {
  /**
   *
   * @type {Array<AdjustedFee>}
   * @memberof CreateStatementRequest
   */
  adjustedFees: Array<AdjustedFee>;
  /**
   *
   * @type {string}
   * @memberof CreateStatementRequest
   */
  date: string;
  /**
   *
   * @type {string}
   * @memberof CreateStatementRequest
   */
  groupUid: string;
  /**
   *
   * @type {string}
   * @memberof CreateStatementRequest
   */
  siteId: string;
  /**
   *
   * @type {string}
   * @memberof CreateStatementRequest
   */
  workItemId: string;
}
/**
 *
 * @export
 * @interface CreateStatementResponse
 */
export interface CreateStatementResponse {
  /**
   *
   * @type {string}
   * @memberof CreateStatementResponse
   */
  statementId: string;
}
/**
 *
 * @export
 * @interface Email
 */
export interface Email {
  /**
   *
   * @type {string}
   * @memberof Email
   */
  email: string;
}
/**
 *
 * @export
 * @interface Group
 */
export interface Group {
  /**
   *
   * @type {string}
   * @memberof Group
   */
  groupName: string;
  /**
   *
   * @type {string}
   * @memberof Group
   */
  accountRef?: string;
  /**
   *
   * @type {string}
   * @memberof Group
   */
  groupId: string;
  /**
   *
   * @type {string}
   * @memberof Group
   */
  businessName?: string;
  /**
   *
   * @type {string}
   * @memberof Group
   */
  businessEmail?: string;
  /**
   *
   * @type {string}
   * @memberof Group
   */
  addressLine1?: string;
  /**
   *
   * @type {string}
   * @memberof Group
   */
  addressLine2?: string;
  /**
   *
   * @type {string}
   * @memberof Group
   */
  town?: string;
  /**
   *
   * @type {string}
   * @memberof Group
   */
  county?: string;
  /**
   *
   * @type {string}
   * @memberof Group
   */
  postcode?: string;
  /**
   *
   * @type {string}
   * @memberof Group
   */
  poNumber?: string;
  /**
   *
   * @type {Array<Site>}
   * @memberof Group
   */
  sites?: Array<Site>;
  /**
   *
   * @type {string}
   * @memberof Group
   */
  stripeConnectedAccountId?: string;
  /**
   *
   * @type {string}
   * @memberof Group
   */
  stripeCustomerId?: string;
  /**
   *
   * @type {string}
   * @memberof Group
   */
  stripeSubscriptionId?: string;
  /**
   *
   * @type {string}
   * @memberof Group
   */
  stripeSubscriptionStatus?: string;
  /**
   *
   * @type {boolean}
   * @memberof Group
   */
  transfersEnabled: boolean;
}
/**
 *
 * @export
 * @interface GroupChargeSummary
 */
export interface GroupChargeSummary {
  /**
   *
   * @type {number}
   * @memberof GroupChargeSummary
   */
  chargingDuration: number;
  /**
   *
   * @type {number}
   * @memberof GroupChargeSummary
   */
  co2Savings: number;
  /**
   *
   * @type {number}
   * @memberof GroupChargeSummary
   */
  energyCost: number;
  /**
   *
   * @type {number}
   * @memberof GroupChargeSummary
   */
  energyDelivered: number;
  /**
   *
   * @type {number}
   * @memberof GroupChargeSummary
   */
  numberOfChargers: number;
  /**
   *
   * @type {number}
   * @memberof GroupChargeSummary
   */
  numberOfCharges: number;
  /**
   *
   * @type {number}
   * @memberof GroupChargeSummary
   */
  numberOfDrivers: number;
  /**
   *
   * @type {number}
   * @memberof GroupChargeSummary
   */
  numberOfSites: number;
  /**
   *
   * @type {number}
   * @memberof GroupChargeSummary
   */
  revenueGenerated: number;
}
/**
 *
 * @export
 * @interface HealthControllerCheck200Response
 */
export interface HealthControllerCheck200Response {
  /**
   *
   * @type {string}
   * @memberof HealthControllerCheck200Response
   */
  status?: string;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck200Response
   */
  info?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck200Response
   */
  error?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck200Response
   */
  details?: { [key: string]: HealthControllerCheck200ResponseInfoValue };
}
/**
 *
 * @export
 * @interface HealthControllerCheck200ResponseInfoValue
 */
export interface HealthControllerCheck200ResponseInfoValue {
  [key: string]: any;

  /**
   *
   * @type {string}
   * @memberof HealthControllerCheck200ResponseInfoValue
   */
  status: string;
}
/**
 *
 * @export
 * @interface HealthControllerCheck503Response
 */
export interface HealthControllerCheck503Response {
  /**
   *
   * @type {string}
   * @memberof HealthControllerCheck503Response
   */
  status?: string;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck503Response
   */
  info?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck503Response
   */
  error?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck503Response
   */
  details?: { [key: string]: HealthControllerCheck200ResponseInfoValue };
}
/**
 *
 * @export
 * @interface InvoiceDto
 */
export interface InvoiceDto {
  /**
   *
   * @type {string}
   * @memberof InvoiceDto
   */
  id: string;
  /**
   *
   * @type {string}
   * @memberof InvoiceDto
   */
  invoiceNumber: string;
  /**
   *
   * @type {string}
   * @memberof InvoiceDto
   */
  quoteNumber: string;
  /**
   *
   * @type {string}
   * @memberof InvoiceDto
   */
  invoiceDate: string;
  /**
   *
   * @type {Group}
   * @memberof InvoiceDto
   */
  group: Group;
  /**
   *
   * @type {Site}
   * @memberof InvoiceDto
   */
  site: Site;
  /**
   *
   * @type {Statement}
   * @memberof InvoiceDto
   */
  statement: Statement;
  /**
   *
   * @type {string}
   * @memberof InvoiceDto
   */
  stripeInvoiceId?: string;
  /**
   *
   * @type {string}
   * @memberof InvoiceDto
   */
  stripeInvoiceStatus?: string;
  /**
   *
   * @type {string}
   * @memberof InvoiceDto
   */
  stripeInvoiceNumber?: string;
}
/**
 *
 * @export
 * @interface Pod
 */
export interface Pod {
  /**
   *
   * @type {number}
   * @memberof Pod
   */
  ageYears?: number;
  /**
   *
   * @type {boolean}
   * @memberof Pod
   */
  confirmChargeEnabled: boolean;
  /**
   *
   * @type {ChargeSummary}
   * @memberof Pod
   */
  chargeSummary?: ChargeSummary;
  /**
   *
   * @type {Coordinates}
   * @memberof Pod
   */
  coordinates: Coordinates;
  /**
   *
   * @type {string}
   * @memberof Pod
   */
  description: string;
  /**
   *
   * @type {number}
   * @memberof Pod
   */
  id: number;
  /**
   *
   * @type {string}
   * @memberof Pod
   */
  installDate?: string;
  /**
   *
   * @type {boolean}
   * @memberof Pod
   */
  isEvZone: boolean;
  /**
   *
   * @type {boolean}
   * @memberof Pod
   */
  isPublic: boolean;
  /**
   *
   * @type {string}
   * @memberof Pod
   */
  lastContact?: string;
  /**
   *
   * @type {string}
   * @memberof Pod
   */
  model: string;
  /**
   *
   * @type {Charge}
   * @memberof Pod
   */
  mostRecentCharge?: Charge;
  /**
   *
   * @type {string}
   * @memberof Pod
   */
  name?: string;
  /**
   *
   * @type {string}
   * @memberof Pod
   */
  ppid: string;
  /**
   *
   * @type {Array<Charge>}
   * @memberof Pod
   */
  recentCharges?: Array<Charge>;
  /**
   *
   * @type {Array<Schedule>}
   * @memberof Pod
   */
  schedules?: Array<Schedule>;
  /**
   *
   * @type {Array<Group>}
   * @memberof Pod
   */
  schemes: Array<Group>;
  /**
   *
   * @type {Site}
   * @memberof Pod
   */
  site?: Site;
  /**
   *
   * @type {Array<Socket>}
   * @memberof Pod
   */
  sockets: Array<Socket>;
  /**
   *
   * @type {string}
   * @memberof Pod
   */
  status: string;
  /**
   *
   * @type {boolean}
   * @memberof Pod
   */
  supportsConfirmCharge?: boolean;
  /**
   *
   * @type {boolean}
   * @memberof Pod
   */
  supportsContactless: boolean;
  /**
   *
   * @type {boolean}
   * @memberof Pod
   */
  supportsEnergyTariff: boolean;
  /**
   *
   * @type {boolean}
   * @memberof Pod
   */
  supportsOcpp?: boolean;
  /**
   *
   * @type {boolean}
   * @memberof Pod
   */
  supportsPerKwh: boolean;
  /**
   *
   * @type {boolean}
   * @memberof Pod
   */
  supportsTariffs: boolean;
  /**
   *
   * @type {TariffSummary}
   * @memberof Pod
   */
  tariff?: TariffSummary;
}
/**
 *
 * @export
 * @interface Schedule
 */
export interface Schedule {
  /**
   *
   * @type {number}
   * @memberof Schedule
   */
  endDay: number;
  /**
   *
   * @type {string}
   * @memberof Schedule
   */
  endTime: string;
  /**
   *
   * @type {boolean}
   * @memberof Schedule
   */
  isActive: boolean;
  /**
   *
   * @type {number}
   * @memberof Schedule
   */
  startDay: number;
  /**
   *
   * @type {string}
   * @memberof Schedule
   */
  startTime: string;
}
/**
 *
 * @export
 * @interface Site
 */
export interface Site {
  /**
   *
   * @type {boolean}
   * @memberof Site
   */
  automated: boolean;
  /**
   *
   * @type {Array<string>}
   * @memberof Site
   */
  emails: Array<string>;
  /**
   *
   * @type {string}
   * @memberof Site
   */
  groupId: string;
  /**
   *
   * @type {string}
   * @memberof Site
   */
  id: string;
  /**
   *
   * @type {string}
   * @memberof Site
   */
  siteId: string;
  /**
   *
   * @type {string}
   * @memberof Site
   */
  siteName: string;
}
/**
 *
 * @export
 * @interface Socket
 */
export interface Socket {
  /**
   *
   * @type {string}
   * @memberof Socket
   */
  door: string;
  /**
   *
   * @type {string}
   * @memberof Socket
   */
  firmwareVersion: string;
  /**
   *
   * @type {boolean}
   * @memberof Socket
   */
  isUpdateAvailable: boolean;
  /**
   *
   * @type {string}
   * @memberof Socket
   */
  lastContact?: string;
  /**
   *
   * @type {string}
   * @memberof Socket
   */
  serialNumber: string;
  /**
   *
   * @type {string}
   * @memberof Socket
   */
  status: SocketStatusEnum;
}

export const SocketStatusEnum = {
  Available: 'Available',
  Charging: 'Charging',
  Offline: 'Offline',
  Unavailable: 'Unavailable',
} as const;

export type SocketStatusEnum =
  (typeof SocketStatusEnum)[keyof typeof SocketStatusEnum];

/**
 *
 * @export
 * @interface Statement
 */
export interface Statement {
  /**
   *
   * @type {string}
   * @memberof Statement
   */
  id: string;
  /**
   *
   * @type {Array<AdjustedFee>}
   * @memberof Statement
   */
  adjustedFees: Array<AdjustedFee>;
  /**
   *
   * @type {boolean}
   * @memberof Statement
   */
  automaticPayout: boolean;
  /**
   *
   * @type {string}
   * @memberof Statement
   */
  emails: string;
  /**
   *
   * @type {object}
   * @memberof Statement
   */
  energy: object;
  /**
   *
   * @type {object}
   * @memberof Statement
   */
  fees: object;
  /**
   *
   * @type {string}
   * @memberof Statement
   */
  groupUid: string;
  /**
   *
   * @type {object}
   * @memberof Statement
   */
  invoice?: object;
  /**
   *
   * @type {number}
   * @memberof Statement
   */
  numberOfCharges: number;
  /**
   *
   * @type {string}
   * @memberof Statement
   */
  payoutStatus?: string;
  /**
   *
   * @type {string}
   * @memberof Statement
   */
  reference: string;
  /**
   *
   * @type {object}
   * @memberof Statement
   */
  revenue: object;
  /**
   *
   * @type {string}
   * @memberof Statement
   */
  siteAddress: string;
  /**
   *
   * @type {WorkItem}
   * @memberof Statement
   */
  workItem?: WorkItem;
}
/**
 *
 * @export
 * @interface Stats
 */
export interface Stats {
  /**
   *
   * @type {number}
   * @memberof Stats
   */
  manual: number;
  /**
   *
   * @type {number}
   * @memberof Stats
   */
  automated: number;
  /**
   *
   * @type {number}
   * @memberof Stats
   */
  total: number;
}
/**
 *
 * @export
 * @interface StatsWorkItems
 */
export interface StatsWorkItems {
  /**
   *
   * @type {WorkItemStatuses}
   * @memberof StatsWorkItems
   */
  workitems: WorkItemStatuses;
}
/**
 *
 * @export
 * @interface SubscriptionInvoiceDto
 */
export interface SubscriptionInvoiceDto {
  /**
   *
   * @type {string}
   * @memberof SubscriptionInvoiceDto
   */
  id: string;
  /**
   *
   * @type {number}
   * @memberof SubscriptionInvoiceDto
   */
  amount: number;
  /**
   *
   * @type {string}
   * @memberof SubscriptionInvoiceDto
   */
  created: string;
  /**
   *
   * @type {string}
   * @memberof SubscriptionInvoiceDto
   */
  due: string;
  /**
   *
   * @type {string}
   * @memberof SubscriptionInvoiceDto
   */
  email: string;
  /**
   *
   * @type {string}
   * @memberof SubscriptionInvoiceDto
   */
  hostedInvoiceUrl: string;
  /**
   *
   * @type {string}
   * @memberof SubscriptionInvoiceDto
   */
  invoiceNumber: string;
  /**
   *
   * @type {string}
   * @memberof SubscriptionInvoiceDto
   */
  invoicePdfUrl?: string;
  /**
   *
   * @type {string}
   * @memberof SubscriptionInvoiceDto
   */
  status: string;
}
/**
 *
 * @export
 * @interface TariffSummary
 */
export interface TariffSummary {
  /**
   *
   * @type {number}
   * @memberof TariffSummary
   */
  id: number;
  /**
   *
   * @type {string}
   * @memberof TariffSummary
   */
  name: string;
}
/**
 *
 * @export
 * @interface UpdateAutomatedStatusRequest
 */
export interface UpdateAutomatedStatusRequest {
  /**
   *
   * @type {boolean}
   * @memberof UpdateAutomatedStatusRequest
   */
  automated: boolean;
}
/**
 *
 * @export
 * @interface UpdateChargerRequest
 */
export interface UpdateChargerRequest {
  /**
   *
   * @type {Array<ChargerWithPod>}
   * @memberof UpdateChargerRequest
   */
  chargers: Array<ChargerWithPod>;
}
/**
 *
 * @export
 * @interface UpdateGroupRequest
 */
export interface UpdateGroupRequest {
  /**
   *
   * @type {string}
   * @memberof UpdateGroupRequest
   */
  businessName: string;
  /**
   *
   * @type {string}
   * @memberof UpdateGroupRequest
   */
  businessEmail: string;
  /**
   *
   * @type {string}
   * @memberof UpdateGroupRequest
   */
  accountRef: string;
  /**
   *
   * @type {string}
   * @memberof UpdateGroupRequest
   */
  addressLine1: string;
  /**
   *
   * @type {string}
   * @memberof UpdateGroupRequest
   */
  addressLine2?: string;
  /**
   *
   * @type {string}
   * @memberof UpdateGroupRequest
   */
  town: string;
  /**
   *
   * @type {string}
   * @memberof UpdateGroupRequest
   */
  county: string;
  /**
   *
   * @type {string}
   * @memberof UpdateGroupRequest
   */
  postcode: string;
  /**
   *
   * @type {string}
   * @memberof UpdateGroupRequest
   */
  poNumber?: string;
}
/**
 *
 * @export
 * @interface UpdateSiteRequest
 */
export interface UpdateSiteRequest {
  /**
   *
   * @type {Array<Email>}
   * @memberof UpdateSiteRequest
   */
  emails: Array<Email>;
  /**
   *
   * @type {boolean}
   * @memberof UpdateSiteRequest
   */
  automated: boolean;
}
/**
 *
 * @export
 * @interface UpdateWorkItemStatusRequest
 */
export interface UpdateWorkItemStatusRequest {
  /**
   *
   * @type {string}
   * @memberof UpdateWorkItemStatusRequest
   */
  status: UpdateWorkItemStatusRequestStatusEnum;
}

export const UpdateWorkItemStatusRequestStatusEnum = {
  New: 'New',
  Ready: 'Ready',
  Generating: 'Generating',
  Generated: 'Generated',
  Sending: 'Sending',
  Sent: 'Sent',
  Cancelled: 'Cancelled',
} as const;

export type UpdateWorkItemStatusRequestStatusEnum =
  (typeof UpdateWorkItemStatusRequestStatusEnum)[keyof typeof UpdateWorkItemStatusRequestStatusEnum];

/**
 *
 * @export
 * @interface WorkItem
 */
export interface WorkItem {
  /**
   *
   * @type {boolean}
   * @memberof WorkItem
   */
  automated: boolean;
  /**
   *
   * @type {string}
   * @memberof WorkItem
   */
  groupUid: string;
  /**
   *
   * @type {string}
   * @memberof WorkItem
   */
  groupName: string;
  /**
   *
   * @type {string}
   * @memberof WorkItem
   */
  id: string;
  /**
   *
   * @type {string}
   * @memberof WorkItem
   */
  month: string;
  /**
   *
   * @type {Statement}
   * @memberof WorkItem
   */
  previousStatement?: Statement;
  /**
   *
   * @type {string}
   * @memberof WorkItem
   */
  siteId: string;
  /**
   *
   * @type {string}
   * @memberof WorkItem
   */
  siteName: string;
  /**
   *
   * @type {Statement}
   * @memberof WorkItem
   */
  statement?: Statement;
  /**
   *
   * @type {string}
   * @memberof WorkItem
   */
  status: WorkItemStatusEnum;
  /**
   *
   * @type {string}
   * @memberof WorkItem
   */
  userId?: string;
  /**
   *
   * @type {string}
   * @memberof WorkItem
   */
  userName?: string;
}

export const WorkItemStatusEnum = {
  New: 'New',
  Ready: 'Ready',
  Generating: 'Generating',
  Generated: 'Generated',
  Sending: 'Sending',
  Sent: 'Sent',
  Cancelled: 'Cancelled',
} as const;

export type WorkItemStatusEnum =
  (typeof WorkItemStatusEnum)[keyof typeof WorkItemStatusEnum];

/**
 *
 * @export
 * @interface WorkItemStatuses
 */
export interface WorkItemStatuses {
  /**
   *
   * @type {Stats}
   * @memberof WorkItemStatuses
   */
  new: Stats;
  /**
   *
   * @type {Stats}
   * @memberof WorkItemStatuses
   */
  ready: Stats;
  /**
   *
   * @type {Stats}
   * @memberof WorkItemStatuses
   */
  generated: Stats;
  /**
   *
   * @type {Stats}
   * @memberof WorkItemStatuses
   */
  sent: Stats;
}

/**
 * DefaultApi - axios parameter creator
 * @export
 */
export const DefaultApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @param {CreateGroupRequest} createGroupRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    groupsControllerAddGroup: async (
      createGroupRequest: CreateGroupRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'createGroupRequest' is not null or undefined
      assertParamExists(
        'groupsControllerAddGroup',
        'createGroupRequest',
        createGroupRequest
      );
      const localVarPath = `/groups`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        createGroupRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    groupsControllerFindAllGroups: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/groups`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} groupId
     * @param {boolean} includeSites
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    groupsControllerFindGroup: async (
      groupId: string,
      includeSites: boolean,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists('groupsControllerFindGroup', 'groupId', groupId);
      // verify required parameter 'includeSites' is not null or undefined
      assertParamExists(
        'groupsControllerFindGroup',
        'includeSites',
        includeSites
      );
      const localVarPath = `/groups/{groupId}`.replace(
        `{${'groupId'}}`,
        encodeURIComponent(String(groupId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (includeSites !== undefined) {
        localVarQueryParameter['includeSites'] = includeSites;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    groupsControllerFindSiteAdminGroups: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/groups/site-admin`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} groupId
     * @param {UpdateGroupRequest} updateGroupRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    groupsControllerUpdateGroup: async (
      groupId: string,
      updateGroupRequest: UpdateGroupRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists('groupsControllerUpdateGroup', 'groupId', groupId);
      // verify required parameter 'updateGroupRequest' is not null or undefined
      assertParamExists(
        'groupsControllerUpdateGroup',
        'updateGroupRequest',
        updateGroupRequest
      );
      const localVarPath = `/groups/{groupId}`.replace(
        `{${'groupId'}}`,
        encodeURIComponent(String(groupId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        updateGroupRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} groupId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    groupsStatementsControllerFindStatementsByGroupId: async (
      groupId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists(
        'groupsStatementsControllerFindStatementsByGroupId',
        'groupId',
        groupId
      );
      const localVarPath = `/groups/{groupId}/statements`.replace(
        `{${'groupId'}}`,
        encodeURIComponent(String(groupId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    healthControllerCheck: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/health`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} invoiceId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    invoiceControllerFindInvoiceById: async (
      invoiceId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'invoiceId' is not null or undefined
      assertParamExists(
        'invoiceControllerFindInvoiceById',
        'invoiceId',
        invoiceId
      );
      const localVarPath = `/invoices/{invoiceId}`.replace(
        `{${'invoiceId'}}`,
        encodeURIComponent(String(invoiceId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} date
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    invoiceControllerGenerateIfsInvoicesExport: async (
      date: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'date' is not null or undefined
      assertParamExists(
        'invoiceControllerGenerateIfsInvoicesExport',
        'date',
        date
      );
      const localVarPath = `/invoices/export/ifs`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (date !== undefined) {
        localVarQueryParameter['date'] = date;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} invoiceId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    invoiceControllerGetInvoicePdf: async (
      invoiceId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'invoiceId' is not null or undefined
      assertParamExists(
        'invoiceControllerGetInvoicePdf',
        'invoiceId',
        invoiceId
      );
      const localVarPath = `/invoices/{invoiceId}/pdf`.replace(
        `{${'invoiceId'}}`,
        encodeURIComponent(String(invoiceId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {object} body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    invoiceControllerReissueStripeInvoices: async (
      body: object,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'body' is not null or undefined
      assertParamExists('invoiceControllerReissueStripeInvoices', 'body', body);
      const localVarPath = `/invoices/reissue`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        body,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} siteId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    sitesControllerFindChargerConfigsWithPods: async (
      siteId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'siteId' is not null or undefined
      assertParamExists(
        'sitesControllerFindChargerConfigsWithPods',
        'siteId',
        siteId
      );
      const localVarPath = `/sites/{siteId}/chargers`.replace(
        `{${'siteId'}}`,
        encodeURIComponent(String(siteId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} siteId
     * @param {UpdateChargerRequest} updateChargerRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    sitesControllerUpdateChargerConfig: async (
      siteId: string,
      updateChargerRequest: UpdateChargerRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'siteId' is not null or undefined
      assertParamExists('sitesControllerUpdateChargerConfig', 'siteId', siteId);
      // verify required parameter 'updateChargerRequest' is not null or undefined
      assertParamExists(
        'sitesControllerUpdateChargerConfig',
        'updateChargerRequest',
        updateChargerRequest
      );
      const localVarPath = `/sites/{siteId}/chargers`.replace(
        `{${'siteId'}}`,
        encodeURIComponent(String(siteId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        updateChargerRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} siteId
     * @param {UpdateSiteRequest} updateSiteRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    sitesControllerUpdateSiteConfig: async (
      siteId: string,
      updateSiteRequest: UpdateSiteRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'siteId' is not null or undefined
      assertParamExists('sitesControllerUpdateSiteConfig', 'siteId', siteId);
      // verify required parameter 'updateSiteRequest' is not null or undefined
      assertParamExists(
        'sitesControllerUpdateSiteConfig',
        'updateSiteRequest',
        updateSiteRequest
      );
      const localVarPath = `/sites/{siteId}`.replace(
        `{${'siteId'}}`,
        encodeURIComponent(String(siteId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        updateSiteRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {CreateStatementRequest} createStatementRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    statementControllerCreateStatement: async (
      createStatementRequest: CreateStatementRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'createStatementRequest' is not null or undefined
      assertParamExists(
        'statementControllerCreateStatement',
        'createStatementRequest',
        createStatementRequest
      );
      const localVarPath = `/statements`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        createStatementRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} month
     * @param {string} groupId
     * @param {string} workItemStatus
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    statementControllerFindAllStatements: async (
      month: string,
      groupId: string,
      workItemStatus: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'month' is not null or undefined
      assertParamExists('statementControllerFindAllStatements', 'month', month);
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists(
        'statementControllerFindAllStatements',
        'groupId',
        groupId
      );
      // verify required parameter 'workItemStatus' is not null or undefined
      assertParamExists(
        'statementControllerFindAllStatements',
        'workItemStatus',
        workItemStatus
      );
      const localVarPath = `/statements`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (month !== undefined) {
        localVarQueryParameter['month'] = month;
      }

      if (groupId !== undefined) {
        localVarQueryParameter['groupId'] = groupId;
      }

      if (workItemStatus !== undefined) {
        localVarQueryParameter['workItemStatus'] = workItemStatus;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} statementId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    statementControllerFindStatement: async (
      statementId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'statementId' is not null or undefined
      assertParamExists(
        'statementControllerFindStatement',
        'statementId',
        statementId
      );
      const localVarPath = `/statements/{statementId}`.replace(
        `{${'statementId'}}`,
        encodeURIComponent(String(statementId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} statementId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    statementControllerGetStatementPdf: async (
      statementId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'statementId' is not null or undefined
      assertParamExists(
        'statementControllerGetStatementPdf',
        'statementId',
        statementId
      );
      const localVarPath = `/statements/{statementId}/pdf`.replace(
        `{${'statementId'}}`,
        encodeURIComponent(String(statementId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {object} body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    stripeControllerCreateStripeConnectedAccountForGroup: async (
      body: object,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'body' is not null or undefined
      assertParamExists(
        'stripeControllerCreateStripeConnectedAccountForGroup',
        'body',
        body
      );
      const localVarPath = `/stripe/connected-accounts`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        body,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {object} body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    stripeControllerCreateStripeConnectedAccountLink: async (
      body: object,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'body' is not null or undefined
      assertParamExists(
        'stripeControllerCreateStripeConnectedAccountLink',
        'body',
        body
      );
      const localVarPath = `/stripe/connected-accounts/account-links`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        body,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {object} body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    stripeControllerCreateStripeCustomerForGroup: async (
      body: object,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'body' is not null or undefined
      assertParamExists(
        'stripeControllerCreateStripeCustomerForGroup',
        'body',
        body
      );
      const localVarPath = `/stripe/customers`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        body,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} customerId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    stripeControllerGetCustomerCashBalance: async (
      customerId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'customerId' is not null or undefined
      assertParamExists(
        'stripeControllerGetCustomerCashBalance',
        'customerId',
        customerId
      );
      const localVarPath =
        `/stripe/customers/{customerId}/cash-balance`.replace(
          `{${'customerId'}}`,
          encodeURIComponent(String(customerId))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {CreateOrUpdateStripeSubscriptionRequest} createOrUpdateStripeSubscriptionRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    stripeControllerCreateStripeSubscriptionForGroup: async (
      createOrUpdateStripeSubscriptionRequest: CreateOrUpdateStripeSubscriptionRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'createOrUpdateStripeSubscriptionRequest' is not null or undefined
      assertParamExists(
        'stripeControllerCreateStripeSubscriptionForGroup',
        'createOrUpdateStripeSubscriptionRequest',
        createOrUpdateStripeSubscriptionRequest
      );
      const localVarPath = `/stripe/subscriptions`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        createOrUpdateStripeSubscriptionRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} subscriptionId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    stripeControllerGetInvoicesBySubscriptionId: async (
      subscriptionId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'subscriptionId' is not null or undefined
      assertParamExists(
        'stripeControllerGetInvoicesBySubscriptionId',
        'subscriptionId',
        subscriptionId
      );
      const localVarPath =
        `/stripe/subscriptions/{subscriptionId}/invoices`.replace(
          `{${'subscriptionId'}}`,
          encodeURIComponent(String(subscriptionId))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} subscriptionId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    stripeControllerGetSubscription: async (
      subscriptionId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'subscriptionId' is not null or undefined
      assertParamExists(
        'stripeControllerGetSubscription',
        'subscriptionId',
        subscriptionId
      );
      const localVarPath = `/stripe/subscriptions/{subscriptionId}`.replace(
        `{${'subscriptionId'}}`,
        encodeURIComponent(String(subscriptionId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} invoiceId
     * @param {string} subscriptionId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    stripeControllerGetSubscriptionInvoice: async (
      invoiceId: string,
      subscriptionId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'invoiceId' is not null or undefined
      assertParamExists(
        'stripeControllerGetSubscriptionInvoice',
        'invoiceId',
        invoiceId
      );
      // verify required parameter 'subscriptionId' is not null or undefined
      assertParamExists(
        'stripeControllerGetSubscriptionInvoice',
        'subscriptionId',
        subscriptionId
      );
      const localVarPath =
        `/stripe/subscriptions/{subscriptionId}/invoices/{invoiceId}`
          .replace(`{${'invoiceId'}}`, encodeURIComponent(String(invoiceId)))
          .replace(
            `{${'subscriptionId'}}`,
            encodeURIComponent(String(subscriptionId))
          );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} invoiceId
     * @param {string} subscriptionId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    stripeControllerGetSubscriptionInvoicePdf: async (
      invoiceId: string,
      subscriptionId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'invoiceId' is not null or undefined
      assertParamExists(
        'stripeControllerGetSubscriptionInvoicePdf',
        'invoiceId',
        invoiceId
      );
      // verify required parameter 'subscriptionId' is not null or undefined
      assertParamExists(
        'stripeControllerGetSubscriptionInvoicePdf',
        'subscriptionId',
        subscriptionId
      );
      const localVarPath =
        `/stripe/subscriptions/{subscriptionId}/invoices/{invoiceId}/pdf`
          .replace(`{${'invoiceId'}}`, encodeURIComponent(String(invoiceId)))
          .replace(
            `{${'subscriptionId'}}`,
            encodeURIComponent(String(subscriptionId))
          );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {object} body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    stripeControllerRemoveStripeConnectedAccountForGroup: async (
      body: object,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'body' is not null or undefined
      assertParamExists(
        'stripeControllerRemoveStripeConnectedAccountForGroup',
        'body',
        body
      );
      const localVarPath = `/stripe/connected-accounts`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'DELETE',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        body,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {CreateOrUpdateStripeSubscriptionRequest} createOrUpdateStripeSubscriptionRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    stripeControllerUpdateStripeSubscriptionForGroup: async (
      createOrUpdateStripeSubscriptionRequest: CreateOrUpdateStripeSubscriptionRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'createOrUpdateStripeSubscriptionRequest' is not null or undefined
      assertParamExists(
        'stripeControllerUpdateStripeSubscriptionForGroup',
        'createOrUpdateStripeSubscriptionRequest',
        createOrUpdateStripeSubscriptionRequest
      );
      const localVarPath = `/stripe/subscriptions`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        createOrUpdateStripeSubscriptionRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} stripeSignature
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    webhookControllerConnectWebhook: async (
      stripeSignature: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'stripeSignature' is not null or undefined
      assertParamExists(
        'webhookControllerConnectWebhook',
        'stripeSignature',
        stripeSignature
      );
      const localVarPath = `/stripe/webhooks/connect`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (stripeSignature != null) {
        localVarHeaderParameter['stripe-signature'] = String(stripeSignature);
      }
      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} stripeSignature
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    webhookControllerWebhook: async (
      stripeSignature: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'stripeSignature' is not null or undefined
      assertParamExists(
        'webhookControllerWebhook',
        'stripeSignature',
        stripeSignature
      );
      const localVarPath = `/stripe/webhooks`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (stripeSignature != null) {
        localVarHeaderParameter['stripe-signature'] = String(stripeSignature);
      }
      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} workItemId
     * @param {AssignUserToWorkItemRequest} assignUserToWorkItemRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    workItemControllerAssignUserToWorkItem: async (
      workItemId: string,
      assignUserToWorkItemRequest: AssignUserToWorkItemRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'workItemId' is not null or undefined
      assertParamExists(
        'workItemControllerAssignUserToWorkItem',
        'workItemId',
        workItemId
      );
      // verify required parameter 'assignUserToWorkItemRequest' is not null or undefined
      assertParamExists(
        'workItemControllerAssignUserToWorkItem',
        'assignUserToWorkItemRequest',
        assignUserToWorkItemRequest
      );
      const localVarPath = `/work-items/{workItemId}/user`.replace(
        `{${'workItemId'}}`,
        encodeURIComponent(String(workItemId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        assignUserToWorkItemRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    workItemControllerCountWorkItems: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/work-items/stats`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    workItemControllerFindAllWorkItems: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/work-items`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} workItemId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    workItemControllerFindWorkItem: async (
      workItemId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'workItemId' is not null or undefined
      assertParamExists(
        'workItemControllerFindWorkItem',
        'workItemId',
        workItemId
      );
      const localVarPath = `/work-items/{workItemId}`.replace(
        `{${'workItemId'}}`,
        encodeURIComponent(String(workItemId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} workItemId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    workItemControllerRemoveUserFromWorkItem: async (
      workItemId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'workItemId' is not null or undefined
      assertParamExists(
        'workItemControllerRemoveUserFromWorkItem',
        'workItemId',
        workItemId
      );
      const localVarPath = `/work-items/{workItemId}/user`.replace(
        `{${'workItemId'}}`,
        encodeURIComponent(String(workItemId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'DELETE',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} workItemId
     * @param {UpdateAutomatedStatusRequest} updateAutomatedStatusRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    workItemControllerUpdateAutomatedStatus: async (
      workItemId: string,
      updateAutomatedStatusRequest: UpdateAutomatedStatusRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'workItemId' is not null or undefined
      assertParamExists(
        'workItemControllerUpdateAutomatedStatus',
        'workItemId',
        workItemId
      );
      // verify required parameter 'updateAutomatedStatusRequest' is not null or undefined
      assertParamExists(
        'workItemControllerUpdateAutomatedStatus',
        'updateAutomatedStatusRequest',
        updateAutomatedStatusRequest
      );
      const localVarPath = `/work-items/{workItemId}/automated`.replace(
        `{${'workItemId'}}`,
        encodeURIComponent(String(workItemId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        updateAutomatedStatusRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} workItemId
     * @param {UpdateSiteRequest} updateSiteRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    workItemControllerUpdateStatementByWorkItemId: async (
      workItemId: string,
      updateSiteRequest: UpdateSiteRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'workItemId' is not null or undefined
      assertParamExists(
        'workItemControllerUpdateStatementByWorkItemId',
        'workItemId',
        workItemId
      );
      // verify required parameter 'updateSiteRequest' is not null or undefined
      assertParamExists(
        'workItemControllerUpdateStatementByWorkItemId',
        'updateSiteRequest',
        updateSiteRequest
      );
      const localVarPath = `/work-items/{workItemId}/statement`.replace(
        `{${'workItemId'}}`,
        encodeURIComponent(String(workItemId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        updateSiteRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} workItemId
     * @param {UpdateWorkItemStatusRequest} updateWorkItemStatusRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    workItemControllerUpdateWorkItemStatus: async (
      workItemId: string,
      updateWorkItemStatusRequest: UpdateWorkItemStatusRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'workItemId' is not null or undefined
      assertParamExists(
        'workItemControllerUpdateWorkItemStatus',
        'workItemId',
        workItemId
      );
      // verify required parameter 'updateWorkItemStatusRequest' is not null or undefined
      assertParamExists(
        'workItemControllerUpdateWorkItemStatus',
        'updateWorkItemStatusRequest',
        updateWorkItemStatusRequest
      );
      const localVarPath = `/work-items/{workItemId}/status`.replace(
        `{${'workItemId'}}`,
        encodeURIComponent(String(workItemId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        updateWorkItemStatusRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * DefaultApi - functional programming interface
 * @export
 */
export const DefaultApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = DefaultApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @param {CreateGroupRequest} createGroupRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async groupsControllerAddGroup(
      createGroupRequest: CreateGroupRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.groupsControllerAddGroup(
          createGroupRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['DefaultApi.groupsControllerAddGroup']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async groupsControllerFindAllGroups(
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<Group>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.groupsControllerFindAllGroups(options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['DefaultApi.groupsControllerFindAllGroups']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} groupId
     * @param {boolean} includeSites
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async groupsControllerFindGroup(
      groupId: string,
      includeSites: boolean,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Group>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.groupsControllerFindGroup(
          groupId,
          includeSites,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['DefaultApi.groupsControllerFindGroup']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async groupsControllerFindSiteAdminGroups(
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<Group>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.groupsControllerFindSiteAdminGroups(
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['DefaultApi.groupsControllerFindSiteAdminGroups']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} groupId
     * @param {UpdateGroupRequest} updateGroupRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async groupsControllerUpdateGroup(
      groupId: string,
      updateGroupRequest: UpdateGroupRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.groupsControllerUpdateGroup(
          groupId,
          updateGroupRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['DefaultApi.groupsControllerUpdateGroup']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} groupId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async groupsStatementsControllerFindStatementsByGroupId(
      groupId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<Array<Statement>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.groupsStatementsControllerFindStatementsByGroupId(
          groupId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DefaultApi.groupsStatementsControllerFindStatementsByGroupId'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async healthControllerCheck(
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<HealthControllerCheck200Response>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.healthControllerCheck(options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['DefaultApi.healthControllerCheck']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} invoiceId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async invoiceControllerFindInvoiceById(
      invoiceId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<InvoiceDto>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.invoiceControllerFindInvoiceById(
          invoiceId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['DefaultApi.invoiceControllerFindInvoiceById']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} date
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async invoiceControllerGenerateIfsInvoicesExport(
      date: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.invoiceControllerGenerateIfsInvoicesExport(
          date,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DefaultApi.invoiceControllerGenerateIfsInvoicesExport'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} invoiceId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async invoiceControllerGetInvoicePdf(
      invoiceId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.invoiceControllerGetInvoicePdf(
          invoiceId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['DefaultApi.invoiceControllerGetInvoicePdf']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {object} body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async invoiceControllerReissueStripeInvoices(
      body: object,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.invoiceControllerReissueStripeInvoices(
          body,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DefaultApi.invoiceControllerReissueStripeInvoices'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} siteId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async sitesControllerFindChargerConfigsWithPods(
      siteId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<Array<ChargerWithPod>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.sitesControllerFindChargerConfigsWithPods(
          siteId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DefaultApi.sitesControllerFindChargerConfigsWithPods'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} siteId
     * @param {UpdateChargerRequest} updateChargerRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async sitesControllerUpdateChargerConfig(
      siteId: string,
      updateChargerRequest: UpdateChargerRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.sitesControllerUpdateChargerConfig(
          siteId,
          updateChargerRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['DefaultApi.sitesControllerUpdateChargerConfig']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} siteId
     * @param {UpdateSiteRequest} updateSiteRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async sitesControllerUpdateSiteConfig(
      siteId: string,
      updateSiteRequest: UpdateSiteRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.sitesControllerUpdateSiteConfig(
          siteId,
          updateSiteRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['DefaultApi.sitesControllerUpdateSiteConfig']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {CreateStatementRequest} createStatementRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async statementControllerCreateStatement(
      createStatementRequest: CreateStatementRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<CreateStatementResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.statementControllerCreateStatement(
          createStatementRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['DefaultApi.statementControllerCreateStatement']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} month
     * @param {string} groupId
     * @param {string} workItemStatus
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async statementControllerFindAllStatements(
      month: string,
      groupId: string,
      workItemStatus: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<Array<Statement>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.statementControllerFindAllStatements(
          month,
          groupId,
          workItemStatus,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['DefaultApi.statementControllerFindAllStatements']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} statementId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async statementControllerFindStatement(
      statementId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Statement>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.statementControllerFindStatement(
          statementId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['DefaultApi.statementControllerFindStatement']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} statementId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async statementControllerGetStatementPdf(
      statementId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.statementControllerGetStatementPdf(
          statementId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['DefaultApi.statementControllerGetStatementPdf']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {object} body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async stripeControllerCreateStripeConnectedAccountForGroup(
      body: object,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.stripeControllerCreateStripeConnectedAccountForGroup(
          body,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DefaultApi.stripeControllerCreateStripeConnectedAccountForGroup'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {object} body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async stripeControllerCreateStripeConnectedAccountLink(
      body: object,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<string>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.stripeControllerCreateStripeConnectedAccountLink(
          body,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DefaultApi.stripeControllerCreateStripeConnectedAccountLink'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {object} body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async stripeControllerCreateStripeCustomerForGroup(
      body: object,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.stripeControllerCreateStripeCustomerForGroup(
          body,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DefaultApi.stripeControllerCreateStripeCustomerForGroup'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} customerId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async stripeControllerGetCustomerCashBalance(
      customerId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<any>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.stripeControllerGetCustomerCashBalance(
          customerId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DefaultApi.stripeControllerGetCustomerCashBalance'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {CreateOrUpdateStripeSubscriptionRequest} createOrUpdateStripeSubscriptionRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async stripeControllerCreateStripeSubscriptionForGroup(
      createOrUpdateStripeSubscriptionRequest: CreateOrUpdateStripeSubscriptionRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.stripeControllerCreateStripeSubscriptionForGroup(
          createOrUpdateStripeSubscriptionRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DefaultApi.stripeControllerCreateStripeSubscriptionForGroup'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} subscriptionId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async stripeControllerGetInvoicesBySubscriptionId(
      subscriptionId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<Array<SubscriptionInvoiceDto>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.stripeControllerGetInvoicesBySubscriptionId(
          subscriptionId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DefaultApi.stripeControllerGetInvoicesBySubscriptionId'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} subscriptionId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async stripeControllerGetSubscription(
      subscriptionId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<object>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.stripeControllerGetSubscription(
          subscriptionId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['DefaultApi.stripeControllerGetSubscription']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} invoiceId
     * @param {string} subscriptionId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async stripeControllerGetSubscriptionInvoice(
      invoiceId: string,
      subscriptionId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<SubscriptionInvoiceDto>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.stripeControllerGetSubscriptionInvoice(
          invoiceId,
          subscriptionId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DefaultApi.stripeControllerGetSubscriptionInvoice'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} invoiceId
     * @param {string} subscriptionId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async stripeControllerGetSubscriptionInvoicePdf(
      invoiceId: string,
      subscriptionId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.stripeControllerGetSubscriptionInvoicePdf(
          invoiceId,
          subscriptionId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DefaultApi.stripeControllerGetSubscriptionInvoicePdf'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {object} body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async stripeControllerRemoveStripeConnectedAccountForGroup(
      body: object,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.stripeControllerRemoveStripeConnectedAccountForGroup(
          body,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DefaultApi.stripeControllerRemoveStripeConnectedAccountForGroup'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {CreateOrUpdateStripeSubscriptionRequest} createOrUpdateStripeSubscriptionRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async stripeControllerUpdateStripeSubscriptionForGroup(
      createOrUpdateStripeSubscriptionRequest: CreateOrUpdateStripeSubscriptionRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.stripeControllerUpdateStripeSubscriptionForGroup(
          createOrUpdateStripeSubscriptionRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DefaultApi.stripeControllerUpdateStripeSubscriptionForGroup'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} stripeSignature
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async webhookControllerConnectWebhook(
      stripeSignature: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.webhookControllerConnectWebhook(
          stripeSignature,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['DefaultApi.webhookControllerConnectWebhook']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} stripeSignature
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async webhookControllerWebhook(
      stripeSignature: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.webhookControllerWebhook(
          stripeSignature,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['DefaultApi.webhookControllerWebhook']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} workItemId
     * @param {AssignUserToWorkItemRequest} assignUserToWorkItemRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async workItemControllerAssignUserToWorkItem(
      workItemId: string,
      assignUserToWorkItemRequest: AssignUserToWorkItemRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.workItemControllerAssignUserToWorkItem(
          workItemId,
          assignUserToWorkItemRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DefaultApi.workItemControllerAssignUserToWorkItem'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async workItemControllerCountWorkItems(
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<StatsWorkItems>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.workItemControllerCountWorkItems(
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['DefaultApi.workItemControllerCountWorkItems']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async workItemControllerFindAllWorkItems(
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<Array<WorkItem>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.workItemControllerFindAllWorkItems(
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['DefaultApi.workItemControllerFindAllWorkItems']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} workItemId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async workItemControllerFindWorkItem(
      workItemId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<WorkItem>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.workItemControllerFindWorkItem(
          workItemId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['DefaultApi.workItemControllerFindWorkItem']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} workItemId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async workItemControllerRemoveUserFromWorkItem(
      workItemId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.workItemControllerRemoveUserFromWorkItem(
          workItemId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DefaultApi.workItemControllerRemoveUserFromWorkItem'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} workItemId
     * @param {UpdateAutomatedStatusRequest} updateAutomatedStatusRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async workItemControllerUpdateAutomatedStatus(
      workItemId: string,
      updateAutomatedStatusRequest: UpdateAutomatedStatusRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.workItemControllerUpdateAutomatedStatus(
          workItemId,
          updateAutomatedStatusRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DefaultApi.workItemControllerUpdateAutomatedStatus'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} workItemId
     * @param {UpdateSiteRequest} updateSiteRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async workItemControllerUpdateStatementByWorkItemId(
      workItemId: string,
      updateSiteRequest: UpdateSiteRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.workItemControllerUpdateStatementByWorkItemId(
          workItemId,
          updateSiteRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DefaultApi.workItemControllerUpdateStatementByWorkItemId'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} workItemId
     * @param {UpdateWorkItemStatusRequest} updateWorkItemStatusRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async workItemControllerUpdateWorkItemStatus(
      workItemId: string,
      updateWorkItemStatusRequest: UpdateWorkItemStatusRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.workItemControllerUpdateWorkItemStatus(
          workItemId,
          updateWorkItemStatusRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DefaultApi.workItemControllerUpdateWorkItemStatus'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * DefaultApi - factory interface
 * @export
 */
export const DefaultApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = DefaultApiFp(configuration);
  return {
    /**
     *
     * @param {CreateGroupRequest} createGroupRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    groupsControllerAddGroup(
      createGroupRequest: CreateGroupRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .groupsControllerAddGroup(createGroupRequest, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    groupsControllerFindAllGroups(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<Group>> {
      return localVarFp
        .groupsControllerFindAllGroups(options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} groupId
     * @param {boolean} includeSites
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    groupsControllerFindGroup(
      groupId: string,
      includeSites: boolean,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Group> {
      return localVarFp
        .groupsControllerFindGroup(groupId, includeSites, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    groupsControllerFindSiteAdminGroups(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<Group>> {
      return localVarFp
        .groupsControllerFindSiteAdminGroups(options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} groupId
     * @param {UpdateGroupRequest} updateGroupRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    groupsControllerUpdateGroup(
      groupId: string,
      updateGroupRequest: UpdateGroupRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .groupsControllerUpdateGroup(groupId, updateGroupRequest, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} groupId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    groupsStatementsControllerFindStatementsByGroupId(
      groupId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<Statement>> {
      return localVarFp
        .groupsStatementsControllerFindStatementsByGroupId(groupId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    healthControllerCheck(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<HealthControllerCheck200Response> {
      return localVarFp
        .healthControllerCheck(options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} invoiceId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    invoiceControllerFindInvoiceById(
      invoiceId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<InvoiceDto> {
      return localVarFp
        .invoiceControllerFindInvoiceById(invoiceId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} date
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    invoiceControllerGenerateIfsInvoicesExport(
      date: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .invoiceControllerGenerateIfsInvoicesExport(date, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} invoiceId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    invoiceControllerGetInvoicePdf(
      invoiceId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .invoiceControllerGetInvoicePdf(invoiceId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {object} body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    invoiceControllerReissueStripeInvoices(
      body: object,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .invoiceControllerReissueStripeInvoices(body, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} siteId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    sitesControllerFindChargerConfigsWithPods(
      siteId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<ChargerWithPod>> {
      return localVarFp
        .sitesControllerFindChargerConfigsWithPods(siteId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} siteId
     * @param {UpdateChargerRequest} updateChargerRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    sitesControllerUpdateChargerConfig(
      siteId: string,
      updateChargerRequest: UpdateChargerRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .sitesControllerUpdateChargerConfig(
          siteId,
          updateChargerRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} siteId
     * @param {UpdateSiteRequest} updateSiteRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    sitesControllerUpdateSiteConfig(
      siteId: string,
      updateSiteRequest: UpdateSiteRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .sitesControllerUpdateSiteConfig(siteId, updateSiteRequest, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {CreateStatementRequest} createStatementRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    statementControllerCreateStatement(
      createStatementRequest: CreateStatementRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<CreateStatementResponse> {
      return localVarFp
        .statementControllerCreateStatement(createStatementRequest, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} month
     * @param {string} groupId
     * @param {string} workItemStatus
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    statementControllerFindAllStatements(
      month: string,
      groupId: string,
      workItemStatus: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<Statement>> {
      return localVarFp
        .statementControllerFindAllStatements(
          month,
          groupId,
          workItemStatus,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} statementId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    statementControllerFindStatement(
      statementId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Statement> {
      return localVarFp
        .statementControllerFindStatement(statementId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} statementId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    statementControllerGetStatementPdf(
      statementId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .statementControllerGetStatementPdf(statementId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {object} body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    stripeControllerCreateStripeConnectedAccountForGroup(
      body: object,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .stripeControllerCreateStripeConnectedAccountForGroup(body, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {object} body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    stripeControllerCreateStripeConnectedAccountLink(
      body: object,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<string> {
      return localVarFp
        .stripeControllerCreateStripeConnectedAccountLink(body, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {object} body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    stripeControllerCreateStripeCustomerForGroup(
      body: object,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .stripeControllerCreateStripeCustomerForGroup(body, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} customerId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    stripeControllerGetCustomerCashBalance(
      customerId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<any> {
      return localVarFp
        .stripeControllerGetCustomerCashBalance(customerId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {CreateOrUpdateStripeSubscriptionRequest} createOrUpdateStripeSubscriptionRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    stripeControllerCreateStripeSubscriptionForGroup(
      createOrUpdateStripeSubscriptionRequest: CreateOrUpdateStripeSubscriptionRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .stripeControllerCreateStripeSubscriptionForGroup(
          createOrUpdateStripeSubscriptionRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} subscriptionId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    stripeControllerGetInvoicesBySubscriptionId(
      subscriptionId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<SubscriptionInvoiceDto>> {
      return localVarFp
        .stripeControllerGetInvoicesBySubscriptionId(subscriptionId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} subscriptionId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    stripeControllerGetSubscription(
      subscriptionId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<object> {
      return localVarFp
        .stripeControllerGetSubscription(subscriptionId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} invoiceId
     * @param {string} subscriptionId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    stripeControllerGetSubscriptionInvoice(
      invoiceId: string,
      subscriptionId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<SubscriptionInvoiceDto> {
      return localVarFp
        .stripeControllerGetSubscriptionInvoice(
          invoiceId,
          subscriptionId,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} invoiceId
     * @param {string} subscriptionId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    stripeControllerGetSubscriptionInvoicePdf(
      invoiceId: string,
      subscriptionId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .stripeControllerGetSubscriptionInvoicePdf(
          invoiceId,
          subscriptionId,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {object} body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    stripeControllerRemoveStripeConnectedAccountForGroup(
      body: object,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .stripeControllerRemoveStripeConnectedAccountForGroup(body, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {CreateOrUpdateStripeSubscriptionRequest} createOrUpdateStripeSubscriptionRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    stripeControllerUpdateStripeSubscriptionForGroup(
      createOrUpdateStripeSubscriptionRequest: CreateOrUpdateStripeSubscriptionRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .stripeControllerUpdateStripeSubscriptionForGroup(
          createOrUpdateStripeSubscriptionRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} stripeSignature
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    webhookControllerConnectWebhook(
      stripeSignature: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .webhookControllerConnectWebhook(stripeSignature, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} stripeSignature
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    webhookControllerWebhook(
      stripeSignature: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .webhookControllerWebhook(stripeSignature, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} workItemId
     * @param {AssignUserToWorkItemRequest} assignUserToWorkItemRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    workItemControllerAssignUserToWorkItem(
      workItemId: string,
      assignUserToWorkItemRequest: AssignUserToWorkItemRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .workItemControllerAssignUserToWorkItem(
          workItemId,
          assignUserToWorkItemRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    workItemControllerCountWorkItems(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<StatsWorkItems> {
      return localVarFp
        .workItemControllerCountWorkItems(options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    workItemControllerFindAllWorkItems(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<WorkItem>> {
      return localVarFp
        .workItemControllerFindAllWorkItems(options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} workItemId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    workItemControllerFindWorkItem(
      workItemId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<WorkItem> {
      return localVarFp
        .workItemControllerFindWorkItem(workItemId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} workItemId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    workItemControllerRemoveUserFromWorkItem(
      workItemId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .workItemControllerRemoveUserFromWorkItem(workItemId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} workItemId
     * @param {UpdateAutomatedStatusRequest} updateAutomatedStatusRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    workItemControllerUpdateAutomatedStatus(
      workItemId: string,
      updateAutomatedStatusRequest: UpdateAutomatedStatusRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .workItemControllerUpdateAutomatedStatus(
          workItemId,
          updateAutomatedStatusRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} workItemId
     * @param {UpdateSiteRequest} updateSiteRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    workItemControllerUpdateStatementByWorkItemId(
      workItemId: string,
      updateSiteRequest: UpdateSiteRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .workItemControllerUpdateStatementByWorkItemId(
          workItemId,
          updateSiteRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} workItemId
     * @param {UpdateWorkItemStatusRequest} updateWorkItemStatusRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    workItemControllerUpdateWorkItemStatus(
      workItemId: string,
      updateWorkItemStatusRequest: UpdateWorkItemStatusRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .workItemControllerUpdateWorkItemStatus(
          workItemId,
          updateWorkItemStatusRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * DefaultApi - object-oriented interface
 * @export
 * @class DefaultApi
 * @extends {BaseAPI}
 */
export class DefaultApi extends BaseAPI {
  /**
   *
   * @param {CreateGroupRequest} createGroupRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public groupsControllerAddGroup(
    createGroupRequest: CreateGroupRequest,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .groupsControllerAddGroup(createGroupRequest, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public groupsControllerFindAllGroups(options?: RawAxiosRequestConfig) {
    return DefaultApiFp(this.configuration)
      .groupsControllerFindAllGroups(options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} groupId
   * @param {boolean} includeSites
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public groupsControllerFindGroup(
    groupId: string,
    includeSites: boolean,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .groupsControllerFindGroup(groupId, includeSites, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public groupsControllerFindSiteAdminGroups(options?: RawAxiosRequestConfig) {
    return DefaultApiFp(this.configuration)
      .groupsControllerFindSiteAdminGroups(options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} groupId
   * @param {UpdateGroupRequest} updateGroupRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public groupsControllerUpdateGroup(
    groupId: string,
    updateGroupRequest: UpdateGroupRequest,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .groupsControllerUpdateGroup(groupId, updateGroupRequest, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} groupId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public groupsStatementsControllerFindStatementsByGroupId(
    groupId: string,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .groupsStatementsControllerFindStatementsByGroupId(groupId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public healthControllerCheck(options?: RawAxiosRequestConfig) {
    return DefaultApiFp(this.configuration)
      .healthControllerCheck(options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} invoiceId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public invoiceControllerFindInvoiceById(
    invoiceId: string,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .invoiceControllerFindInvoiceById(invoiceId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} date
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public invoiceControllerGenerateIfsInvoicesExport(
    date: string,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .invoiceControllerGenerateIfsInvoicesExport(date, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} invoiceId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public invoiceControllerGetInvoicePdf(
    invoiceId: string,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .invoiceControllerGetInvoicePdf(invoiceId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {object} body
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public invoiceControllerReissueStripeInvoices(
    body: object,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .invoiceControllerReissueStripeInvoices(body, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} siteId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public sitesControllerFindChargerConfigsWithPods(
    siteId: string,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .sitesControllerFindChargerConfigsWithPods(siteId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} siteId
   * @param {UpdateChargerRequest} updateChargerRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public sitesControllerUpdateChargerConfig(
    siteId: string,
    updateChargerRequest: UpdateChargerRequest,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .sitesControllerUpdateChargerConfig(siteId, updateChargerRequest, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} siteId
   * @param {UpdateSiteRequest} updateSiteRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public sitesControllerUpdateSiteConfig(
    siteId: string,
    updateSiteRequest: UpdateSiteRequest,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .sitesControllerUpdateSiteConfig(siteId, updateSiteRequest, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {CreateStatementRequest} createStatementRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public statementControllerCreateStatement(
    createStatementRequest: CreateStatementRequest,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .statementControllerCreateStatement(createStatementRequest, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} month
   * @param {string} groupId
   * @param {string} workItemStatus
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public statementControllerFindAllStatements(
    month: string,
    groupId: string,
    workItemStatus: string,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .statementControllerFindAllStatements(
        month,
        groupId,
        workItemStatus,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} statementId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public statementControllerFindStatement(
    statementId: string,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .statementControllerFindStatement(statementId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} statementId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public statementControllerGetStatementPdf(
    statementId: string,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .statementControllerGetStatementPdf(statementId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {object} body
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public stripeControllerCreateStripeConnectedAccountForGroup(
    body: object,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .stripeControllerCreateStripeConnectedAccountForGroup(body, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {object} body
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public stripeControllerCreateStripeConnectedAccountLink(
    body: object,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .stripeControllerCreateStripeConnectedAccountLink(body, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {object} body
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public stripeControllerCreateStripeCustomerForGroup(
    body: object,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .stripeControllerCreateStripeCustomerForGroup(body, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} customerId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public stripeControllerGetCustomerCashBalance(
    customerId: string,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .stripeControllerGetCustomerCashBalance(customerId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {CreateOrUpdateStripeSubscriptionRequest} createOrUpdateStripeSubscriptionRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public stripeControllerCreateStripeSubscriptionForGroup(
    createOrUpdateStripeSubscriptionRequest: CreateOrUpdateStripeSubscriptionRequest,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .stripeControllerCreateStripeSubscriptionForGroup(
        createOrUpdateStripeSubscriptionRequest,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} subscriptionId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public stripeControllerGetInvoicesBySubscriptionId(
    subscriptionId: string,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .stripeControllerGetInvoicesBySubscriptionId(subscriptionId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} subscriptionId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public stripeControllerGetSubscription(
    subscriptionId: string,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .stripeControllerGetSubscription(subscriptionId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} invoiceId
   * @param {string} subscriptionId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public stripeControllerGetSubscriptionInvoice(
    invoiceId: string,
    subscriptionId: string,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .stripeControllerGetSubscriptionInvoice(
        invoiceId,
        subscriptionId,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} invoiceId
   * @param {string} subscriptionId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public stripeControllerGetSubscriptionInvoicePdf(
    invoiceId: string,
    subscriptionId: string,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .stripeControllerGetSubscriptionInvoicePdf(
        invoiceId,
        subscriptionId,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {object} body
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public stripeControllerRemoveStripeConnectedAccountForGroup(
    body: object,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .stripeControllerRemoveStripeConnectedAccountForGroup(body, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {CreateOrUpdateStripeSubscriptionRequest} createOrUpdateStripeSubscriptionRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public stripeControllerUpdateStripeSubscriptionForGroup(
    createOrUpdateStripeSubscriptionRequest: CreateOrUpdateStripeSubscriptionRequest,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .stripeControllerUpdateStripeSubscriptionForGroup(
        createOrUpdateStripeSubscriptionRequest,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} stripeSignature
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public webhookControllerConnectWebhook(
    stripeSignature: string,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .webhookControllerConnectWebhook(stripeSignature, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} stripeSignature
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public webhookControllerWebhook(
    stripeSignature: string,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .webhookControllerWebhook(stripeSignature, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} workItemId
   * @param {AssignUserToWorkItemRequest} assignUserToWorkItemRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public workItemControllerAssignUserToWorkItem(
    workItemId: string,
    assignUserToWorkItemRequest: AssignUserToWorkItemRequest,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .workItemControllerAssignUserToWorkItem(
        workItemId,
        assignUserToWorkItemRequest,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public workItemControllerCountWorkItems(options?: RawAxiosRequestConfig) {
    return DefaultApiFp(this.configuration)
      .workItemControllerCountWorkItems(options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public workItemControllerFindAllWorkItems(options?: RawAxiosRequestConfig) {
    return DefaultApiFp(this.configuration)
      .workItemControllerFindAllWorkItems(options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} workItemId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public workItemControllerFindWorkItem(
    workItemId: string,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .workItemControllerFindWorkItem(workItemId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} workItemId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public workItemControllerRemoveUserFromWorkItem(
    workItemId: string,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .workItemControllerRemoveUserFromWorkItem(workItemId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} workItemId
   * @param {UpdateAutomatedStatusRequest} updateAutomatedStatusRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public workItemControllerUpdateAutomatedStatus(
    workItemId: string,
    updateAutomatedStatusRequest: UpdateAutomatedStatusRequest,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .workItemControllerUpdateAutomatedStatus(
        workItemId,
        updateAutomatedStatusRequest,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} workItemId
   * @param {UpdateSiteRequest} updateSiteRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public workItemControllerUpdateStatementByWorkItemId(
    workItemId: string,
    updateSiteRequest: UpdateSiteRequest,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .workItemControllerUpdateStatementByWorkItemId(
        workItemId,
        updateSiteRequest,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} workItemId
   * @param {UpdateWorkItemStatusRequest} updateWorkItemStatusRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public workItemControllerUpdateWorkItemStatus(
    workItemId: string,
    updateWorkItemStatusRequest: UpdateWorkItemStatusRequest,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .workItemControllerUpdateWorkItemStatus(
        workItemId,
        updateWorkItemStatusRequest,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}
