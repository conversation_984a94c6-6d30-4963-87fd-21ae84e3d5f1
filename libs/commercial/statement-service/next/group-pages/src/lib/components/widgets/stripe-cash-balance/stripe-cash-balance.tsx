import {
  Card,
  Heading,
  HeadingSizes,
  Paragraph,
} from '@experience/shared/react/design-system';
import { Group } from '@experience/commercial/statement-service/shared';
import { RetrieveCashBalanceResponse } from '@experience/shared/nest/stripe';
import { formatPenceAsCurrencyString } from '@experience/shared/typescript/utils';

export interface StripeCashBalanceProps {
  group: Group;
  cashBalance?: RetrieveCashBalanceResponse;
}

export const StripeCashBalance = ({ group, cashBalance }: StripeCashBalanceProps) => {
  const hasCashBalance = cashBalance && cashBalance.availableBalance !== undefined;

  return (
    <Card>
      <Card.Header className="pb-1">Cash Balance</Card.Header>
      {group.stripeCustomerId ? (
        <>
          <Heading.H3
            fontSize={HeadingSizes.XS}
            className="font-bold pb-2 pt-2"
          >
            Available Balance
          </Heading.H3>
          <Paragraph className="text-2xl font-bold">
            {hasCashBalance 
              ? formatPenceAsCurrencyString({ amount: cashBalance.availableBalance })
              : '£0.00'
            }
          </Paragraph>
          <Paragraph className="text-sm text-gray-600 mt-1">
            Virtual bank account balance
          </Paragraph>
        </>
      ) : (
        <Paragraph className="text-center text-gray-500">
          Create a Stripe customer to view cash balance
        </Paragraph>
      )}
    </Card>
  );
};
