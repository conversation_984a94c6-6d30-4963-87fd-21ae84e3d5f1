import {
  Alert,
  AlertType,
  Heading,
  Paragraph,
} from '@experience/shared/react/design-system';
import { BusinessAddress } from '../../components/widgets/business-address/business-address';
import { BusinessDetails } from '../../components/widgets/business-details/business-details';
import { EditGroupModal } from '../../components/modals/edit-group-modal/edit-group-modal';
import {
  Group,
  Statement,
  SubscriptionInvoiceDto,
} from '@experience/commercial/statement-service/shared';
import { PageHeader } from '@experience/shared/react/headings';
import { RetrieveSubscriptionResponse } from '@experience/shared/nest/stripe';
import { SiteTable } from '../../components/tables/site-table/site-table';
import {
  StatementsTable,
  getStatementsTableColumnDefinitions,
} from '@experience/commercial/statement-service/next/statements-pages';
import { StripeCustomerDetails } from '../../components/widgets/stripe-customer-details/stripe-customer-details';
import { StripePayoutAccountDetails } from '../../components/widgets/stripe-payout-account-details/stripe-payout-account-details';
import { StripeSubscriptions } from '../../components/widgets/stripe-subscriptions/stripe-subscriptions';
import { SubscriptionPaymentsTable } from '../../components/tables/subscription-payments-table/subscription-payments-table';
import {
  ThreeColumnLayout,
  TwoColumnLayout,
  VerticalSpacer,
  VerticalSpacerSize,
} from '@experience/shared/react/layouts';
import { useFeature } from 'flagged';
import { useSearchParams } from 'next/navigation';
import { useState } from 'react';

export interface GroupPageProps {
  group: Group;
  payments: SubscriptionInvoiceDto[];
  subscription?: RetrieveSubscriptionResponse;
  statements: Statement[];
}

export const GroupPage = ({
  group,
  payments,
  statements,
  subscription,
}: GroupPageProps) => {
  const searchParams = useSearchParams();
  const isEditGroupOpen = searchParams.get('openEditGroup');
  const [openEditGroup, setOpenEditGroup] = useState(
    isEditGroupOpen === 'true'
  );
  const columns = getStatementsTableColumnDefinitions();
  const reissueStripeInvoices = useFeature('reissueStripeInvoices') as boolean;

  return (
    <>
      <PageHeader
        heading={group.groupName}
        subHeading="The business details used to populate invoices for sites in this group"
      />
      {group.stripeCustomerId && reissueStripeInvoices ? (
        <>
          <Alert type={AlertType.INFO}>
            <Paragraph>
              Editing business details for this customer will reissue any open
              or overdue invoices
            </Paragraph>
          </Alert>
          <VerticalSpacer />
        </>
      ) : null}
      <TwoColumnLayout>
        <BusinessAddress group={group} setOpenEditGroup={setOpenEditGroup} />
        <BusinessDetails group={group} setOpenEditGroup={setOpenEditGroup} />
      </TwoColumnLayout>
      <VerticalSpacer />
      <ThreeColumnLayout>
        <StripeCustomerDetails group={group} />
        <StripeSubscriptions group={group} subscription={subscription} />
        <StripePayoutAccountDetails group={group} />
      </ThreeColumnLayout>
      <VerticalSpacer />
      <SiteTable sites={group.sites ?? []} groupId={group.groupId} />
      <VerticalSpacer size={VerticalSpacerSize.Large} />
      <Heading.H2>Statements</Heading.H2>
      <VerticalSpacer />
      <StatementsTable
        columns={columns.slice(1)}
        filters={{
          clientSide: [],
          serverSide: [],
        }}
        serverFilters={false}
        statements={statements}
      />
      <VerticalSpacer size={VerticalSpacerSize.Large} />
      <Heading.H2>Subscription payments</Heading.H2>
      <VerticalSpacer />
      <SubscriptionPaymentsTable
        payments={payments}
        subscriptionId={subscription?.id ?? ''}
      />
      <EditGroupModal
        group={group}
        open={openEditGroup}
        setOpen={setOpenEditGroup}
      />
    </>
  );
};

export default GroupPage;
