import {
  Card,
  Heading,
  HeadingSizes,
  Paragraph,
} from '@experience/shared/react/design-system';
import { CashBalance } from '@experience/commercial/site-admin/typescript/domain-model';
import { formatPenceAsCurrencyString } from '@experience/shared/typescript/utils';

export interface CashBalanceProps {
  cashBalance?: CashBalance;
}

export const CashBalanceWidget = ({ cashBalance }: CashBalanceProps) => {
  const hasCashBalance =
    cashBalance && cashBalance.availableBalance !== undefined;

  return (
    <Card>
      <Card.Header className="pb-1">Cash Balance</Card.Header>
      <Heading.H3 fontSize={HeadingSizes.XS} className="font-bold pb-2 pt-2">
        Available Balance
      </Heading.H3>
      <Paragraph className="text-2xl font-bold">
        {hasCashBalance
          ? formatPenceAsCurrencyString({
              amount: cashBalance.availableBalance,
            })
          : '£0.00'}
      </Paragraph>
      <Paragraph className="text-sm text-gray-600 mt-1">
        Virtual bank account balance
      </Paragraph>
    </Card>
  );
};
