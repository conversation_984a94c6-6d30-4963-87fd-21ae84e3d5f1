import { BillingInformation } from '@experience/commercial/site-admin/typescript/domain-model';
import { BusinessAddress } from '../../components/widgets/business-address/business-address';
import { BusinessDetails } from '../../components/widgets/business-details/business-details';
import { CashBalanceWidget } from '../../components/widgets/cash-balance/cash-balance';
import { DemoModeButton } from '@experience/commercial/site-admin/next/shared';
import { ErrorPage } from '@experience/shared/react/error-pages';
import {
  LoadingOverlay,
  NoBillingIcon,
  Placeholder,
  Tab,
  Tabs,
} from '@experience/shared/react/design-system';
import { OnboardingLink } from '../../components/onboarding-link/onboarding-link';
import { PageHeaderWithGroupName } from '@experience/commercial/site-admin/next/admins-pages';
import {
  ThreeColumnLayout,
  TwoColumnLayout,
  VerticalSpacer,
} from '@experience/shared/react/layouts';
import { useState } from 'react';
import BillingStatementsTable from '../../components/tables/billing-statements-table/billing-statements-table';
import SubscriptionPaymentsTable from '../../components/tables/subscription-payments-table/subscription-payments-table';
import useSWR from 'swr';

export const BillingPage = () => {
  const [, setTabId] = useState<number>(0);
  const placeHolder = (
    <>
      <PageHeaderWithGroupName heading="Billing" />
      <div className="pt-24" />
      <Placeholder
        icon={<NoBillingIcon.SOLID />}
        searchInput={<DemoModeButton />}
        text="You don't have any billing information"
      />
    </>
  );

  const { data: billingInfo, error } =
    useSWR<BillingInformation>('/api/billing');

  if (error) {
    return error.status === 404 ? placeHolder : <ErrorPage />;
  }

  if (!billingInfo) {
    return <LoadingOverlay />;
  }

  const {
    customerDetails,
    statements,
    subscription,
    onboardingLink,
    cashBalance,
  } = billingInfo;

  return (
    <>
      <PageHeaderWithGroupName heading="Billing" />
      {onboardingLink ? (
        <>
          <OnboardingLink onboardingLink={onboardingLink} />
          <VerticalSpacer />
        </>
      ) : null}
      <Tabs onChange={(id) => setTabId(id)}>
        <Tab title="Statements">
          <BillingStatementsTable billingStatements={statements} />
        </Tab>
        <Tab title="Subscription">
          <SubscriptionPaymentsTable
            subscriptionsPayments={subscription.invoices}
          />
        </Tab>
        <Tab title="Business details">
          <TwoColumnLayout>
            <BusinessAddress customerDetails={customerDetails} />
            <BusinessDetails customerDetails={customerDetails} />
          </TwoColumnLayout>
          <VerticalSpacer />
          <ThreeColumnLayout>
            <CashBalanceWidget cashBalance={cashBalance} />
          </ThreeColumnLayout>
        </Tab>
      </Tabs>
    </>
  );
};

export default BillingPage;
