import {
  BillingInformation,
  BillingStatement,
  CashBalance,
  CustomerDetails,
  Subscription,
  SubscriptionInvoice,
} from '@experience/commercial/site-admin/typescript/domain-model';
import {
  Group,
  Statement,
  DefaultApi as StatementServiceApi,
  SubscriptionInvoiceDto,
} from '@experience/commercial/statement-service/axios';
import { GroupNotFoundException } from './billing.exception';
import { Injectable, Logger } from '@nestjs/common';
import { InvoiceDto } from '@experience/commercial/statement-service/shared';

@Injectable()
export class BillingService {
  private readonly logger = new Logger(BillingService.name);

  constructor(private readonly statementServiceApi: StatementServiceApi) {}

  async findBillingInformationByGroupUid(
    groupUid: string
  ): Promise<BillingInformation> {
    this.logger.log('finding billing information');

    const groupConfig = await this.statementServiceApi
      .groupsControllerFindGroup(groupUid, false)
      .then((response) => response.data)
      .catch((error) => {
        if (error?.response?.status === 404) {
          throw new GroupNotFoundException();
        }
        throw error;
      });

    const statements = await this.statementServiceApi
      .groupsStatementsControllerFindStatementsByGroupId(groupConfig.groupId)
      .then((response) => this.mapStatements(response.data));

    const subscriptionInvoices = groupConfig.stripeSubscriptionId
      ? await this.statementServiceApi
          .stripeControllerGetInvoicesBySubscriptionId(
            groupConfig.stripeSubscriptionId
          )
          .then((response) => this.mapSubscriptionInvoices(response.data))
      : [];

    const onboardingLink =
      groupConfig.stripeConnectedAccountId && !groupConfig.transfersEnabled
        ? await this.statementServiceApi
            .stripeControllerCreateStripeConnectedAccountLink({
              groupId: groupUid,
            })
            .then(({ data }) => data)
        : undefined;

    return {
      customerDetails: this.mapGroupConfigToBillingInformation(groupConfig),
      statements,
      subscription: this.mapSubscription(groupConfig, subscriptionInvoices),
      onboardingLink,
    };
  }

  private mapGroupConfigToBillingInformation(group: Group): CustomerDetails {
    return {
      address: {
        line1: group.addressLine1,
        line2: group.addressLine2,
        town: group.town,
        county: group.county,
        postcode: group.postcode,
      },
      email: group.businessEmail,
      name: group.businessName,
      accountReference: group.accountRef,
      poNumber: group.poNumber,
    };
  }

  private mapStatements(statements: Statement[]): BillingStatement[] {
    return statements.map((statement) => {
      const invoice = statement.invoice as InvoiceDto;
      return {
        siteName: statement.workItem.siteName,
        month: statement.workItem.month,
        feeInvoiceNumber:
          invoice?.stripeInvoiceNumber ?? invoice?.invoiceNumber,
        feeInvoiceStatus: invoice?.stripeInvoiceStatus,
        invoiceId: invoice?.id,
        revenuePayoutStatus: statement.payoutStatus,
        statementId: statement.id,
      };
    });
  }

  private mapSubscriptionInvoices(
    subscriptions: SubscriptionInvoiceDto[]
  ): SubscriptionInvoice[] {
    return subscriptions.map((subscription) => ({
      status: subscription.status,
      created: subscription.created,
      amount: subscription.amount,
      due: subscription.due,
      hostedInvoiceUrl: subscription.hostedInvoiceUrl,
      invoiceNumber: subscription.invoiceNumber,
      invoicePdfUrl: subscription.invoicePdfUrl,
      customerEmail: subscription.email,
    }));
  }

  private mapSubscription(
    groupConfig: Group,
    subscriptionInvoices: SubscriptionInvoice[]
  ): Subscription {
    return {
      status: groupConfig.stripeSubscriptionStatus,
      invoices: subscriptionInvoices,
    };
  }
}
