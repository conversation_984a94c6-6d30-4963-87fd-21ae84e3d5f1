export class CustomerDetails {
  address?: {
    line1?: string;
    line2?: string;
    town?: string;
    county?: string;
    postcode?: string;
  };
  email: string;
  name: string;
  accountReference?: string;
  poNumber?: string;
}

export class BillingStatement {
  siteName: string;
  month: string;
  feeInvoiceNumber: string;
  feeInvoiceStatus: string;
  invoiceId: string;
  revenuePayoutStatus: string;
  statementId: string;
}

export class SubscriptionInvoice {
  status: string;
  created: string;
  amount: number;
  due: string;
  hostedInvoiceUrl: string;
  invoiceNumber: string;
  invoicePdfUrl: string;
  customerEmail: string;
}

export class Subscription {
  status: string;
  invoices: SubscriptionInvoice[];
}

export class CashBalance {
  availableBalance: number;
  currency: string;
}

export class BillingInformation {
  customerDetails: CustomerDetails;
  onboardingLink: string | undefined;
  statements: BillingStatement[];
  subscription: Subscription;
  cashBalance?: CashBalance;
}
