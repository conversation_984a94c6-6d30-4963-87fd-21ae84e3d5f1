import {
  AuthHeaders,
  userFromToken,
} from '@experience/mobile/nest/authorisation';
import {
  CanActivate,
  ExecutionContext,
  HttpStatus,
  Injectable,
  Logger,
  UnauthorizedException,
} from '@nestjs/common';
import { FlagsService } from '@experience/shared/nest/remote-config';
import { TokenValidator } from '@experience/mobile/nest/authorisation';
import type { JWTPayload } from 'jose';
import type { Request } from 'express';

@Injectable()
export class AuthGuardToken implements CanActivate {
  private logger = new Logger('HTTP');

  constructor(
    private readonly tokenValidator: TokenValidator,
    private readonly flagsService: FlagsService
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const decodedToken = await this.validateToken(context);

    await this.validateEmailVerification(decodedToken);

    return true;
  }

  public async validateToken(context: ExecutionContext) {
    const request = context.switchToHttp().getRequest();
    const authHeaders = this.extractTokenFromHeader(request);
    if (!authHeaders.token) {
      throw new UnauthorizedException();
    }

    try {
      await this.tokenValidator.validateTokens(authHeaders);
    } catch (error) {
      this.logger.error(error);
      throw new UnauthorizedException();
    }

    return await this.attachUserToRequest(authHeaders.token, request);
  }

  private async validateEmailVerification(decodedToken: JWTPayload) {
    if (
      decodedToken.email_verified !== undefined &&
      !decodedToken.email_verified
    ) {
      throw new UnauthorizedException({
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
        cause: 'Email is not verified',
      });
    }
  }

  public async attachUserToRequest(token: string, request: Request) {
    const decodedToken = this.tokenValidator.getDecodedToken(token);

    request['user'] = {
      ...userFromToken(decodedToken),
    };

    return decodedToken;
  }

  private extractTokenFromHeader(request: Request): AuthHeaders {
    const [type, token] = (request.headers?.authorization || '').split(' ');
    const appVersion = request.headers['x-app-version'];
    return (type || '').toUpperCase() === 'BEARER'
      ? ({ token, appVersion } as AuthHeaders)
      : ({ token: undefined, appVersion } as AuthHeaders);
  }
}
