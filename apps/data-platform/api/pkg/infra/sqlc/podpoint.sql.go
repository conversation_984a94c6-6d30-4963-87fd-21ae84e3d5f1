// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: podpoint.sql

package sqlc

import (
	"context"
	"database/sql"

	"github.com/lib/pq"
)

const chargerExists = `-- name: ChargerExists :one
SELECT EXISTS (SELECT id
               FROM podpoint.pod_units
               WHERE ppid = $1)
`

func (q *Queries) ChargerExists(ctx context.Context, ppid string) (bool, error) {
	row := q.db.QueryRowContext(ctx, chargerExists, ppid)
	var exists bool
	err := row.Scan(&exists)
	return exists, err
}

const findAddressByID = `-- name: FindAddressByID :one
SELECT id, group_id, contact_name, email, telephone, business_name, line_1, line_2, postal_town, postcode, country, description, type_id, tariff_id, cost_per_kwh, created_at, updated_at, deleted_at
FROM podpoint.pod_addresses
WHERE id = $1
`

func (q *Queries) FindAddressByID(ctx context.Context, id int64) (PodpointPodAddress, error) {
	row := q.db.QueryRowContext(ctx, findAddressByID, id)
	var i PodpointPodAddress
	err := row.Scan(
		&i.ID,
		&i.GroupID,
		&i.ContactName,
		&i.Email,
		&i.Telephone,
		&i.BusinessName,
		&i.Line1,
		&i.Line2,
		&i.PostalTown,
		&i.Postcode,
		&i.Country,
		&i.Description,
		&i.TypeID,
		&i.TariffID,
		&i.CostPerKwh,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const findGroupByID = `-- name: FindGroupByID :one
SELECT id, uid, type_id, owner_user_id, name, contact_name, phone, business_name, line_1, line_2, postal_town, postcode, country, fee_percentage, created_at, updated_at, deleted_at
FROM podpoint.groups
WHERE id = $1
`

func (q *Queries) FindGroupByID(ctx context.Context, id int64) (PodpointGroup, error) {
	row := q.db.QueryRowContext(ctx, findGroupByID, id)
	var i PodpointGroup
	err := row.Scan(
		&i.ID,
		&i.Uid,
		&i.TypeID,
		&i.OwnerUserID,
		&i.Name,
		&i.ContactName,
		&i.Phone,
		&i.BusinessName,
		&i.Line1,
		&i.Line2,
		&i.PostalTown,
		&i.Postcode,
		&i.Country,
		&i.FeePercentage,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const findGroupByUUID = `-- name: FindGroupByUUID :one
SELECT id, uid, type_id, owner_user_id, name, contact_name, phone, business_name, line_1, line_2, postal_town, postcode, country, fee_percentage, created_at, updated_at, deleted_at
FROM podpoint.groups
WHERE uid = $1
`

func (q *Queries) FindGroupByUUID(ctx context.Context, uid string) (PodpointGroup, error) {
	row := q.db.QueryRowContext(ctx, findGroupByUUID, uid)
	var i PodpointGroup
	err := row.Scan(
		&i.ID,
		&i.Uid,
		&i.TypeID,
		&i.OwnerUserID,
		&i.Name,
		&i.ContactName,
		&i.Phone,
		&i.BusinessName,
		&i.Line1,
		&i.Line2,
		&i.PostalTown,
		&i.Postcode,
		&i.Country,
		&i.FeePercentage,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const findLocationByID = `-- name: FindLocationByID :one
SELECT id, address_id, revenue_profile_id, advert_id, longitude, latitude, geohash, name, description, payg_enabled, contactless_enabled, midmeter_enabled, is_public, is_home, is_ev_zone, unit_id, created_at, updated_at, deleted_at, timezone, uuid
FROM podpoint.pod_locations
WHERE id = $1
`

func (q *Queries) FindLocationByID(ctx context.Context, id int64) (PodpointPodLocation, error) {
	row := q.db.QueryRowContext(ctx, findLocationByID, id)
	var i PodpointPodLocation
	err := row.Scan(
		&i.ID,
		&i.AddressID,
		&i.RevenueProfileID,
		&i.AdvertID,
		&i.Longitude,
		&i.Latitude,
		&i.Geohash,
		&i.Name,
		&i.Description,
		&i.PaygEnabled,
		&i.ContactlessEnabled,
		&i.MidmeterEnabled,
		&i.IsPublic,
		&i.IsHome,
		&i.IsEvZone,
		&i.UnitID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.Timezone,
		&i.Uuid,
	)
	return i, err
}

const findUserByUUID = `-- name: FindUserByUUID :one
SELECT id, auth_id, group_id, role_id, email, salutation, first_name, last_name, password, remember_token, verify_token, last_login, activated_at, last_notification_seen, created_at, updated_at, deleted_at, is_emailed_usage_data
FROM podpoint.users
WHERE auth_id = $1
`

func (q *Queries) FindUserByUUID(ctx context.Context, authID string) (PodpointUser, error) {
	row := q.db.QueryRowContext(ctx, findUserByUUID, authID)
	var i PodpointUser
	err := row.Scan(
		&i.ID,
		&i.AuthID,
		&i.GroupID,
		&i.RoleID,
		&i.Email,
		&i.Salutation,
		&i.FirstName,
		&i.LastName,
		&i.Password,
		&i.RememberToken,
		&i.VerifyToken,
		&i.LastLogin,
		&i.ActivatedAt,
		&i.LastNotificationSeen,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.IsEmailedUsageData,
	)
	return i, err
}

const retrieveDriverCharges = `-- name: RetrieveDriverCharges :many
SELECT pu.name                                                            AS unit_name,
       pa.business_name,
       c.starts_at,
       c.ends_at,
       CASE
         WHEN c.kwh_used = 0.00 THEN 0
         WHEN c.duration IS NULL THEN EXTRACT(EPOCH FROM (c.ends_at - c.starts_at))
         ELSE c.duration
         END::integer                                                     AS charging_duration,
       COALESCE(EXTRACT(EPOCH FROM (c.ends_at - c.starts_at)), 0)::bigint AS plugged_in_duration,
       c.kwh_used,
       c.energy_cost                                                      AS charge_cost,
       COALESCE(ABS(be.presentment_amount), 0)::bigint                    AS revenue_generated
FROM podpoint.charges c
       INNER JOIN podpoint.billing_accounts ba ON c.billing_account_id = ba.id
       INNER JOIN podpoint.billing_events be ON c.billing_event_id = be.id
       INNER JOIN podpoint.users u ON ba.user_id = u.id
       INNER JOIN podpoint.pod_units pu ON c.unit_id = pu.id
       INNER JOIN podpoint.pod_locations pl ON c.location_id = pl.id
       INNER JOIN podpoint.pod_addresses pa ON pl.address_id = pa.id
       INNER JOIN podpoint.groups g ON pa.group_id = g.id
WHERE u.auth_id = $1
  AND g.uid = $2
  AND c.ends_at BETWEEN $3 AND $4
  AND c.ends_at IS NOT NULL
  AND u.deleted_at IS NULL
  AND c.deleted_at IS NULL
  AND ba.deleted_at IS NULL
  AND be.deleted_at IS NULL
  AND pu.deleted_at IS NULL
  AND pl.deleted_at IS NULL
  AND pa.deleted_at IS NULL
  AND g.deleted_at IS NULL
ORDER BY c.ends_at DESC
`

type RetrieveDriverChargesParams struct {
	AuthID   string       `json:"auth_id"`
	Uid      string       `json:"uid"`
	EndsAt   sql.NullTime `json:"ends_at"`
	EndsAt_2 sql.NullTime `json:"ends_at_2"`
}

type RetrieveDriverChargesRow struct {
	UnitName          sql.NullString `json:"unit_name"`
	BusinessName      string         `json:"business_name"`
	StartsAt          sql.NullTime   `json:"starts_at"`
	EndsAt            sql.NullTime   `json:"ends_at"`
	ChargingDuration  int32          `json:"charging_duration"`
	PluggedInDuration int64          `json:"plugged_in_duration"`
	KwhUsed           string         `json:"kwh_used"`
	ChargeCost        sql.NullInt32  `json:"charge_cost"`
	RevenueGenerated  int64          `json:"revenue_generated"`
}

func (q *Queries) RetrieveDriverCharges(ctx context.Context, arg RetrieveDriverChargesParams) ([]RetrieveDriverChargesRow, error) {
	rows, err := q.db.QueryContext(ctx, retrieveDriverCharges,
		arg.AuthID,
		arg.Uid,
		arg.EndsAt,
		arg.EndsAt_2,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []RetrieveDriverChargesRow
	for rows.Next() {
		var i RetrieveDriverChargesRow
		if err := rows.Scan(
			&i.UnitName,
			&i.BusinessName,
			&i.StartsAt,
			&i.EndsAt,
			&i.ChargingDuration,
			&i.PluggedInDuration,
			&i.KwhUsed,
			&i.ChargeCost,
			&i.RevenueGenerated,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const retrieveOrganisationDriverCharges = `-- name: RetrieveOrganisationDriverCharges :many
SELECT u.id                                                                    AS user_id,
       u.auth_id                                                               AS auth_id,
       u.email,
       u.first_name,
       u.last_name,
       COUNT(c.*)                                                              AS number_of_charges,
       COALESCE(ABS(SUM(be.presentment_amount)), 0)::bigint                    AS revenue_generated,
       COALESCE(ROUND(SUM(c.kwh_used), 2), 0)::numeric(18, 2)                  AS total_energy_usage,
       COALESCE(SUM(c.energy_cost), 0)::bigint                                 AS energy_cost,
       SUM(CASE
             WHEN c.kwh_used = 0.00 THEN 0
             WHEN c.duration IS NULL THEN EXTRACT(EPOCH FROM (c.ends_at - c.starts_at))
             ELSE c.duration
         END
         )                                                                     AS charging_duration,
       COALESCE(SUM(EXTRACT(EPOCH FROM (c.ends_at - c.starts_at))), 0)::bigint AS plugged_in_duration
FROM podpoint.charges c
       INNER JOIN podpoint.billing_accounts ba ON c.billing_account_id = ba.id
       INNER JOIN podpoint.billing_events be ON c.billing_event_id = be.id
       INNER JOIN podpoint.users u ON ba.user_id = u.id
       INNER JOIN podpoint.pod_units pu ON c.unit_id = pu.id
       INNER JOIN podpoint.pod_locations pl ON c.location_id = pl.id
       INNER JOIN podpoint.pod_addresses pa ON pl.address_id = pa.id
       INNER JOIN podpoint.groups g ON pa.group_id = g.id
WHERE u.auth_id = ANY ($1::varchar[])
  AND g.uid = $2
  AND c.ends_at BETWEEN $3 AND $4
  AND c.ends_at IS NOT NULL
  AND u.deleted_at IS NULL
  AND c.deleted_at IS NULL
  AND ba.deleted_at IS NULL
  AND be.deleted_at IS NULL
  AND pu.deleted_at IS NULL
  AND pl.deleted_at IS NULL
  AND pa.deleted_at IS NULL
  AND g.deleted_at IS NULL
GROUP BY u.last_name, u.first_name, u.email, u.auth_id, u.id
`

type RetrieveOrganisationDriverChargesParams struct {
	Column1  []string     `json:"column_1"`
	Uid      string       `json:"uid"`
	EndsAt   sql.NullTime `json:"ends_at"`
	EndsAt_2 sql.NullTime `json:"ends_at_2"`
}

type RetrieveOrganisationDriverChargesRow struct {
	UserID            int64  `json:"user_id"`
	AuthID            string `json:"auth_id"`
	Email             string `json:"email"`
	FirstName         string `json:"first_name"`
	LastName          string `json:"last_name"`
	NumberOfCharges   int64  `json:"number_of_charges"`
	RevenueGenerated  int64  `json:"revenue_generated"`
	TotalEnergyUsage  string `json:"total_energy_usage"`
	EnergyCost        int64  `json:"energy_cost"`
	ChargingDuration  int64  `json:"charging_duration"`
	PluggedInDuration int64  `json:"plugged_in_duration"`
}

func (q *Queries) RetrieveOrganisationDriverCharges(ctx context.Context, arg RetrieveOrganisationDriverChargesParams) ([]RetrieveOrganisationDriverChargesRow, error) {
	rows, err := q.db.QueryContext(ctx, retrieveOrganisationDriverCharges,
		pq.Array(arg.Column1),
		arg.Uid,
		arg.EndsAt,
		arg.EndsAt_2,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []RetrieveOrganisationDriverChargesRow
	for rows.Next() {
		var i RetrieveOrganisationDriverChargesRow
		if err := rows.Scan(
			&i.UserID,
			&i.AuthID,
			&i.Email,
			&i.FirstName,
			&i.LastName,
			&i.NumberOfCharges,
			&i.RevenueGenerated,
			&i.TotalEnergyUsage,
			&i.EnergyCost,
			&i.ChargingDuration,
			&i.PluggedInDuration,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const userExists = `-- name: UserExists :one
SELECT EXISTS (SELECT id
               FROM podpoint.users
               WHERE auth_id = $1)
`

func (q *Queries) UserExists(ctx context.Context, authID string) (bool, error) {
	row := q.db.QueryRowContext(ctx, userExists, authID)
	var exists bool
	err := row.Scan(&exists)
	return exists, err
}
