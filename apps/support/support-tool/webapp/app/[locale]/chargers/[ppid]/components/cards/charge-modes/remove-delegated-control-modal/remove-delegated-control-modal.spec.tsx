import 'whatwg-fetch';
import { RemoveDelegatedControlModal } from './remove-delegated-control-modal';
import { Toaster } from '@experience/shared/react/design-system';
import { fireEvent, screen, waitFor } from '@testing-library/react';
import { mockCharger } from '@experience/support/support-tool/shared/specs';
import { removeFromDelegatedControl } from '../../../../actions/remove-from-delegated-control';
import { renderWithProviders } from '../../../../../../test-utils';

jest.mock('../../../../actions/remove-from-delegated-control');
const mockRemoveDelegatedControl = jest.mocked(removeFromDelegatedControl);
const mockSetOpen = jest.fn();

const defaultProps = {
  ppid: mockCharger.summary.ppid,
  open: true,
  setOpen: mockSetOpen,
};

describe('Remove from delegated control', () => {
  it('should render correctly', () => {
    const { baseElement } = renderWithProviders(
      <RemoveDelegatedControlModal {...defaultProps} />
    );
    expect(baseElement).toBeInTheDocument();
  });

  it('should match snapshot', () => {
    const { baseElement } = renderWithProviders(
      <RemoveDelegatedControlModal {...defaultProps} />
    );
    expect(baseElement).toMatchSnapshot();
  });

  it('should call the remove delegated control action and show a confirm toast if successful', async () => {
    renderWithProviders(
      <>
        <Toaster />
        <RemoveDelegatedControlModal {...defaultProps} />
      </>
    );

    fireEvent.click(screen.getByRole('button', { name: 'Confirm' }));

    expect(mockRemoveDelegatedControl).toHaveBeenCalledWith(
      mockCharger.summary.ppid
    );
    await waitFor(() => {
      expect(
        screen.getByText('Charger no longer under delegated control')
      ).toBeInTheDocument();
    });
  });

  it('should show error toast if there is an error removing delegated control', async () => {
    mockRemoveDelegatedControl.mockRejectedValueOnce(new Error('error'));

    renderWithProviders(
      <>
        <Toaster />
        <RemoveDelegatedControlModal {...defaultProps} />
      </>
    );

    fireEvent.click(screen.getByRole('button', { name: 'Confirm' }));

    await waitFor(() => {
      expect(
        screen.getByText('Error when removing from delegated control')
      ).toBeInTheDocument();
    });
  });
});
