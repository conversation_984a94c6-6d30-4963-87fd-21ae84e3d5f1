import {
  Group,
  STATEMENT_SERVICE_API_URL,
  Statement,
  SubscriptionInvoiceDto,
} from '@experience/commercial/statement-service/shared';
import { GroupPage } from '@experience/commercial/statement-service/next/group-pages';
import { RetrieveSubscriptionResponse, RetrieveCashBalanceResponse } from '@experience/shared/nest/stripe';
import { appRequestHandler } from '@experience/commercial/next/app-request-utils';
import { notFound } from 'next/navigation';

export const metadata = {
  title: 'Commercial Statement Service - Group info',
  description: 'Commercial Statement Service group info page',
};

export const dynamic = 'force-dynamic';
export const revalidate = 0;

const GroupInfo = async (props: { params: Promise<{ groupId: string }> }) => {
  const params = await props.params;
  const group = await appRequestHandler<Group>(
    `${STATEMENT_SERVICE_API_URL}/groups/${params.groupId}?includeSites=true`
  );

  if (!group) {
    return notFound();
  }

  const payments = group.stripeSubscriptionId
    ? await appRequestHandler<SubscriptionInvoiceDto[]>(
        `${STATEMENT_SERVICE_API_URL}/stripe/subscriptions/${group.stripeSubscriptionId}/invoices`
      )
    : [];

  const subscription = group.stripeSubscriptionId
    ? await appRequestHandler<RetrieveSubscriptionResponse>(
        `${STATEMENT_SERVICE_API_URL}/stripe/subscriptions/${group.stripeSubscriptionId}`
      )
    : undefined;

  const statements =
    (await appRequestHandler<Statement[]>(
      `${STATEMENT_SERVICE_API_URL}/groups/${group.groupId}/statements`
    )) ?? [];

  const cashBalance = group.stripeCustomerId
    ? await appRequestHandler<RetrieveCashBalanceResponse>(
        `${STATEMENT_SERVICE_API_URL}/stripe/customers/${group.stripeCustomerId}/cash-balance`
      )
    : undefined;

  return (
    <GroupPage
      group={group}
      payments={payments}
      subscription={subscription}
      statements={statements}
      cashBalance={cashBalance}
    />
  );
};

export default GroupInfo;
