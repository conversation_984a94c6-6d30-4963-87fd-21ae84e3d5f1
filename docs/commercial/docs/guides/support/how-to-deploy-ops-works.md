# How to deploy to Ops Works

## The purpose of this article

This article describes how to deploy a PHP application using AWS OpsWorks.

## Trigger

A pull request is ready to test OR has been merged to master and needs deploying.

## Before you begin

- A branch you wish to deploy
- Access to the 'Pod Point' AWS account

## Deploying to staging

### AWS

1. Navigate to 'Management console' using `AWS-ADFS-Software-Engineer` under the 'Pod Point' account.
2. Navigate to the 'OpsWorks' service.
3. Find the stack you want to deploy to and click through e.g 'MIS Staging'.
4. Navigate to 'Deployments' and check who deployed last - you may want to double-check with them if the environment is free.

### Slack

5. Post in #software-environments that you are planning to deploy to staging.

### AWS

6. Navigate to 'Apps', navigate through to the app and click 'Edit'.
7. Change the 'Branch/Revision' field to your branch and click 'Save'.

### Slack

8. Double check no one has responded to your deployment message - it can be polite to give people a minute or two to respond.

### AWS

9. Navigate to 'Apps' and click 'Deploy'.
10. Add your branch to the 'Comment' field and click Deploy.
11. The application should redeploy over the course of a couple of minutes, perhaps longer if any migrations are present.

### Slack

12. Monitor the #software-bugs-qa and #software-alarms channels.

## Deploying to production

### AWS

1. Navigate to 'Management console' using `AWS-ADFS-Software-Engineer` under the 'Pod Point' account.
2. Navigate to the 'OpsWorks' service.
3. Find the stack you want to deploy to and click through e.g 'MIS'.
4. Navigate to 'Apps', navigate through to the app and check that 'master' is deployed - sometimes feature branches are used, and you will need to double-check with the developer.

### Slack

5. Post in #software-environments that you are planning to deploy.
6. Double check no one has responded to your deployment message - it can be polite to give people a minute or two to respond.

### Github

7. Merge your branch to master.

### AWS

8. Navigate back to 'Apps' and click 'Deploy'.
9. Add your branch to the 'Comment' field and click Deploy.
10. The application should redeploy over the course of a couple of minutes, perhaps longer if any migrations are present.

### Slack and AWS

11. Monitor the #software-bugs and #software-alarms channels and any relevant Grafana dashboards.
