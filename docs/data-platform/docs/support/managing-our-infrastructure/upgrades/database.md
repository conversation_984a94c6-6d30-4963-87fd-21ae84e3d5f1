# Upgrade minor version of experience-data-platform PostgreSQL database

Runbook:

1. Ensure **no** `Pending Maintenance` items are present on the Aurora cluster and instances. 
   1. If maintenance is pending either wait for the maintenance window to take care of them *or* apply them by setting the `apply_immediately` cluster property to `true` as described below (requires a terraform run) and applying them on the AWS admin console. 
2. Update the Postgres image version in `experience` repository and raise a PR.
   1. https://github.com/Pod-Point/experience/blob/main/libs/data-platform/db-test/postgres/docker-compose.yml#L5
   2. https://github.com/Pod-Point/experience/blob/main/libs/shared/test/db/experience/docker-compose.yml#L8 
   3. https://github.com/Pod-Point/experience/blob/main/libs/shared/go/db/postgres/test/containers.go#L27
3. If PR checks pass, update the Postgres image version in `terraform` repository and raise a PR.
   1. https://github.com/Pod-Point/terraform/blob/main/modules/applications/destination/aurora.tf#L11
4. Set `experience_db_cluster_apply_immediately` variable to `true` in Terraform Enterprise for `dev`, `staging` and `prod`
   of `experience-commercial` workspaces in https://terraform-enterprise.pod-point.com/app/pod-point/workspaces.
5. On `dev`, `staging` and `prod` workspaces use the 'Start new run' action to apply the `experience_db_cluster_apply_immediately` variable new value to the cluster.
6. Merge the PR in `terraform` repository.
7. Apply the changes in the following order for environments: `dev`, `staging`, `prod`. Before applying on `staging` make sure that 
   `dev` apply was successful and similarly - before applying on `prod` make sure that `staging` was successful.
8. Check in the AWS console if the version is upgraded. Go to RDS -> Databases -> experience. Select instance and look for version:
   <br/>![Version](../assets/upgrades-database-check.png)
9. Set `experience_db_cluster_apply_immediately` variable to `false` in Terraform Enterprise for `dev`, `staging` and `prod`
   of `experience-commercial` workspaces.
10. On `dev`, `staging` and `prod` workspaces use the 'Start new run' action to apply the `experience_db_cluster_apply_immediately` variable new value to the cluster:
    <br/>![ApplyImmediate](../assets/apply-immediately.png)
11. Merge the PR in `experience` repository.
